# Mobile API Documentation

This documentation covers all mobile endpoints for the Tree Management System with actual API responses.

## Base URL
```
{{api}}/mobile/
```

## Authentication

### Login
**POST** `/mobile/auth`

**Request Body:**
```json
{
    "phone": "*********",
    "password": "otabek!@123"
}
```

**Response (200):**
```json
{
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2OGM5NjZhZTRiMzY5NDBlMzFiMTI1NWEiLCJmaXJzdE5hbWUiOiJKb2huIiwibGFzdE5hbWUiOiJEb2UiLCJtaWRkbGVOYW1lIjoiQUxpeWVlZSIsInJvbGUiOiJ3b3JrZXIiLCJpYXQiOjE3NTgxOTUzMjIsImV4cCI6MTc1ODgwMDEyMn0.afdEPQMNrCQFTqIiyxpRvv4790FnfIgjTNEAlhDZ_kM"
}
```

---

## Province Management

### Get All Provinces
**GET** `/mobile/province/getAll`

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)

**Headers:**
- `Authorization: Bearer {{token}}`

**Response (200):**
```json
{
    "docs": [
        {
            "_id": "68c93ac950aa3600f9c45f16",
            "title": "And<PERSON>jon viloyati",
            "desc": "Shahrixon hududi",
            "createdAt": "2025-09-16T10:24:09.969Z",
            "updatedAt": "2025-09-16T10:24:09.969Z"
        },
        {
            "_id": "68c93d6950aa3600f9c45f28",
            "title": "Farg'ona viloyati",
            "desc": "markaz",
            "createdAt": "2025-09-16T10:35:21.048Z",
            "updatedAt": "2025-09-16T10:35:21.048Z"
        },
        {
            "_id": "68c93e2750aa3600f9c45f40",
            "title": "Namangan viloyati",
            "desc": "Gullar sayliii",
            "createdAt": "2025-09-16T10:38:31.623Z",
            "updatedAt": "2025-09-16T10:45:49.513Z"
        }
    ],
    "totalDocs": 3,
    "limit": 10,
    "totalPages": 1,
    "page": 1,
    "pagingCounter": 1,
    "hasPrevPage": false,
    "hasNextPage": false,
    "prevPage": null,
    "nextPage": null
}
```

### Get Single Province
**GET** `/mobile/province/getOne/{id}`

**Parameters:**
- `id`: Province ID

**Headers:**
- `Authorization: Bearer {{token}}`

---

## Variety Management

### Get All Varieties
**GET** `/mobile/variety/getAll`

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)

**Headers:**
- `Authorization: Bearer {{token}}`

**Response (200):**
```json
{
    "docs": [
        {
            "_id": "68c94b7050aa3600f9c460e2",
            "title": "Manzarali daraxt",
            "createdAt": "2025-09-16T11:35:12.419Z",
            "updatedAt": "2025-09-16T11:35:12.419Z"
        },
        {
            "_id": "68c94bea57e455a4cca3c1c2",
            "title": "Mevali daraxt",
            "createdAt": "2025-09-16T11:37:14.934Z",
            "updatedAt": "2025-09-16T11:37:14.934Z"
        },
        {
            "_id": "68c94c7457e455a4cca3c1dc",
            "title": "Sitrus meva",
            "createdAt": "2025-09-16T11:39:32.350Z",
            "updatedAt": "2025-09-16T11:39:32.350Z"
        }
    ],
    "totalDocs": 3,
    "limit": 10,
    "totalPages": 1,
    "page": 1,
    "pagingCounter": 1,
    "hasPrevPage": false,
    "hasNextPage": false,
    "prevPage": null,
    "nextPage": null
}
```

### Get Single Variety
**GET** `/mobile/variety/getOne/{id}`

**Parameters:**
- `id`: Variety ID

**Headers:**
- `Authorization: Bearer {{token}}`

---

## Region Management

### Get All Regions
**GET** `/mobile/region/getAll`

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `province`: Province ID to filter regions

**Headers:**
- `Authorization: Bearer {{token}}`

**Response (200):**
```json
{
    "docs": [
        {
            "_id": "68c9410f50aa3600f9c45f83",
            "title": "Rishton",
            "desc": "Janub",
            "province": {
                "_id": "68c93d6950aa3600f9c45f28",
                "title": "Farg'ona viloyati",
                "desc": "markaz",
                "createdAt": "2025-09-16T10:35:21.048Z",
                "updatedAt": "2025-09-16T10:35:21.048Z"
            },
            "createdAt": "2025-09-16T10:50:55.474Z",
            "updatedAt": "2025-09-16T10:50:55.474Z"
        },
        {
            "_id": "68c9418050aa3600f9c45f8f",
            "title": "Beshariq tumani",
            "desc": "o'rmonchilik",
            "province": {
                "_id": "68c93d6950aa3600f9c45f28",
                "title": "Farg'ona viloyati",
                "desc": "markaz",
                "createdAt": "2025-09-16T10:35:21.048Z",
                "updatedAt": "2025-09-16T10:35:21.048Z"
            },
            "createdAt": "2025-09-16T10:52:48.083Z",
            "updatedAt": "2025-09-16T10:57:22.293Z"
        }
    ],
    "totalDocs": 3,
    "limit": 10,
    "totalPages": 1,
    "page": 1,
    "pagingCounter": 1,
    "hasPrevPage": false,
    "hasNextPage": false,
    "prevPage": null,
    "nextPage": null
}
```

### Get Single Region
**GET** `/mobile/region/getOne/{id}`

**Parameters:**
- `id`: Region ID

**Headers:**
- `Authorization: Bearer {{token}}`

---

## Tree Management

### Get All Trees
**GET** `/mobile/tree/getAll`

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `variety` (optional): Variety ID to filter trees

**Headers:**
- `Authorization: Bearer {{token}}`

**Response (200):**
```json
{
    "docs": [
        {
            "_id": "68c962744b36940e31b12517",
            "title": "Limon",
            "variety": {
                "_id": "68c94c7457e455a4cca3c1dc",
                "title": "Sitrus meva",
                "createdAt": "2025-09-16T11:39:32.350Z",
                "updatedAt": "2025-09-16T11:39:32.350Z"
            },
            "price": 30000,
            "count": 350,
            "createdAt": "2025-09-16T13:13:24.498Z",
            "updatedAt": "2025-09-16T13:23:58.885Z"
        },
        {
            "_id": "68c964224b36940e31b12534",
            "title": "Olma",
            "variety": {
                "_id": "68c94bea57e455a4cca3c1c2",
                "title": "Mevali daraxt",
                "createdAt": "2025-09-16T11:37:14.934Z",
                "updatedAt": "2025-09-16T11:37:14.934Z"
            },
            "price": 15000,
            "count": 400,
            "createdAt": "2025-09-16T13:20:34.342Z",
            "updatedAt": "2025-09-16T13:20:34.342Z"
        }
    ],
    "totalDocs": 2,
    "limit": 10,
    "totalPages": 1,
    "page": 1,
    "pagingCounter": 1,
    "hasPrevPage": false,
    "hasNextPage": false,
    "prevPage": null,
    "nextPage": null
}
```

### Get Single Tree
**GET** `/mobile/tree/getOne/{id}`

**Parameters:**
- `id`: Tree ID

**Headers:**
- `Authorization: Bearer {{token}}`

---

## District Management

### Get All Districts
**GET** `/mobile/district/getAll`

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `province` (optional): Province ID to filter districts
- `region` (optional): Region ID to filter districts

**Headers:**
- `Authorization: Bearer {{token}}`

**Response (200):**
```json
{
    "docs": [
        {
            "_id": "68c942d550aa3600f9c45fb9",
            "title": "Arabxona",
            "desc": "sentr",
            "province": {
                "_id": "68c93d6950aa3600f9c45f28",
                "title": "Farg'ona viloyati",
                "desc": "markaz",
                "createdAt": "2025-09-16T10:35:21.048Z",
                "updatedAt": "2025-09-16T10:35:21.048Z"
            },
            "region": {
                "_id": "68c941fc50aa3600f9c45f9e",
                "title": "Oltiariq",
                "desc": "uzumchilik",
                "province": "68c93d6950aa3600f9c45f28",
                "createdAt": "2025-09-16T10:54:52.909Z",
                "updatedAt": "2025-09-16T10:54:52.909Z"
            },
            "createdAt": "2025-09-16T10:58:29.007Z",
            "updatedAt": "2025-09-16T11:31:18.817Z"
        },
        {
            "_id": "68c9478950aa3600f9c45ff1",
            "title": "Qipchoq",
            "desc": "avvalgi oqtepa",
            "province": {
                "_id": "68c93d6950aa3600f9c45f28",
                "title": "Farg'ona viloyati",
                "desc": "markaz",
                "createdAt": "2025-09-16T10:35:21.048Z",
                "updatedAt": "2025-09-16T10:35:21.048Z"
            },
            "region": {
                "_id": "68c9418050aa3600f9c45f8f",
                "title": "Beshariq tumani",
                "desc": "o'rmonchilik",
                "province": "68c93d6950aa3600f9c45f28",
                "createdAt": "2025-09-16T10:52:48.083Z",
                "updatedAt": "2025-09-16T10:57:22.293Z"
            },
            "createdAt": "2025-09-16T11:18:33.924Z",
            "updatedAt": "2025-09-16T11:31:29.322Z"
        }
    ],
    "totalDocs": 3,
    "limit": 10,
    "totalPages": 1,
    "page": 1,
    "pagingCounter": 1,
    "hasPrevPage": false,
    "hasNextPage": false,
    "prevPage": null,
    "nextPage": null
}
```

### Get Single District
**GET** `/mobile/district/getOne/{id}`

**Parameters:**
- `id`: District ID

**Headers:**
- `Authorization: Bearer {{token}}`

---

## User Management

### Get Single User
**GET** `/mobile/user/getOne/{id}`

**Parameters:**
- `id`: User ID

**Headers:**
- `Authorization: Bearer {{token}}`

**Response (200):**
```json
{
    "_id": "68c966ae4b36940e31b1255a",
    "firstName": "John",
    "lastName": "Doe",
    "middleName": "ALiyeee",
    "phone": "*********",
    "password": "otabek!@123",
    "role": "worker",
    "active": true,
    "createdAt": "2025-09-16T13:31:26.516Z",
    "updatedAt": "2025-09-17T04:59:30.810Z"
}
```

---

## Client Management

### Add Client
**POST** `/mobile/client`

**Request Body:**
```json
{
    "fullName": "John",
    "birthDay": "Doe",
    "passport": "*********",
    "phone": "*********",
    "jshshir": "1234567890123456",
    "address": "Farg'ona 15/17 uy",
    "province": "68c90849253faefe26b09e53",
    "region": "68c90960f288c4f1d38c8e97"
}
```

**Headers:**
- `Authorization: Bearer {{token}}`
- `Content-Type: application/json`

**Response (201):**
```json
{
    "message": "success"
}
```

### Get All Clients
**GET** `/mobile/client/getAll`

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `province` (optional): Province ID to filter clients
- `region` (optional): Region ID to filter clients
- `search` (optional): Search term for filtering

**Headers:**
- `Authorization: Bearer {{token}}`

**Response (200):**
```json
{
    "docs": [
        {
            "_id": "68cbed939ac3a6ccb56aa2f6",
            "fullName": "John",
            "birthDay": "Doe",
            "phone": "*********",
            "passport": "*********",
            "jshshir": "1234567890123456",
            "address": "Farg'ona 15/17 uy",
            "province": null,
            "region": null,
            "createdAt": "2025-09-18T11:31:31.843Z",
            "updatedAt": "2025-09-18T11:31:31.843Z"
        }
    ],
    "totalDocs": 1,
    "limit": 10,
    "totalPages": 1,
    "page": 1,
    "pagingCounter": 1,
    "hasPrevPage": false,
    "hasNextPage": false,
    "prevPage": null,
    "nextPage": null
}
```

### Get Single Client
**GET** `/mobile/client/getOne/{id}`

**Parameters:**
- `id`: Client ID

**Headers:**
- `Authorization: Bearer {{token}}`

### Update Client
**PUT** `/mobile/client/{id}`

**Parameters:**
- `id`: Client ID

**Request Body:**
```json
{
    "fullName": "John",
    "birthDay": "Doe",
    "passport": "*********",
    "phone": "*********",
    "jshshir": "1234567890123456",
    "address": "Farg'ona 15/17 uy",
    "province": "68c90849253faefe26b09e53",
    "region": "68c90960f288c4f1d38c8e97"
}
```

**Headers:**
- `Authorization: Bearer {{token}}`
- `Content-Type: application/json`

**Response (200):**
```json
{
    "message": "success"
}
```

---

## Order Management

### Create Order
**POST** `/mobile/order`

**Content-Type:** `multipart/form-data`

**Form Data:**
- `client`: Client ID (e.g., "68c9246141cd23bb4a26639d")
- `files`: Upload files (images) - multiple files supported
- `totalPrice`: Total order price (e.g., "10000")
- `paymentType`: Payment type (e.g., "1")
- `province`: Province ID
- `region`: Region ID
- `district`: District ID
- `file`: PDF file - contract
- `products[0][tree]`: Tree ID for first product
- `products[0][count]`: Quantity for first product (e.g., "2")
- `products[0][price]`: Unit price for first product (e.g., "5000")
- `products[0][totalPrice]`: Total price for first product (e.g., "10000")
- `location[longitude]`: Location longitude (e.g., "40.121312")
- `location[latitude]`: Location latitude (e.g., "70.125465")

**Headers:**
- `Authorization: Bearer {{token}}`

**Response (201):**
```json
{
    "message": "success"
}
```

### Get All Orders
**GET** `/mobile/order/getAll`

**Query Parameters:**
- `page` (optional): Page number
- `limit` (optional): Items per page
- `status` (optional): Order status filter
- `date` (optional): Date filter (format: YYYY-MM-DD)

**Headers:**
- `Authorization: Bearer {{token}}`

**Response (200):**
```json
[
    {
        "_id": "68cbee3c9ac3a6ccb56aa302",
        "client": null,
        "worker": "68c966ae4b36940e31b1255a",
        "photos": [],
        "location": {
            "longitude": "40.121312",
            "latitude": "70.125465"
        },
        "products": [
            {
                "tree": null,
                "count": 2,
                "price": 5000,
                "totalPrice": 10000,
                "_id": "68cbee3c9ac3a6ccb56aa303"
            }
        ],
        "totalPrice": 10000,
        "paymentType": 1,
        "date": "2025-09-18",
        "time": "16:34",
        "status": 2,
        "province": null,
        "region": null,
        "district": null,
        "pdf": null,
        "createdAt": "2025-09-18T11:34:20.681Z",
        "updatedAt": "2025-09-18T11:34:20.681Z"
    }
]
```

### Get Single Order
**GET** `/mobile/order/getOne/{id}`

**Parameters:**
- `id`: Order ID

**Headers:**
- `Authorization: Bearer {{token}}`

---

## Response Structure

### Paginated Response
All list endpoints return paginated data with the following structure:
```json
{
    "docs": [...], // Array of items
    "totalDocs": 10, // Total number of documents
    "limit": 10, // Items per page limit
    "totalPages": 1, // Total number of pages
    "page": 1, // Current page number
    "pagingCounter": 1, // Page counter
    "hasPrevPage": false, // Has previous page
    "hasNextPage": false, // Has next page
    "prevPage": null, // Previous page number
    "nextPage": null // Next page number
}
```

### Success Response
Simple success operations return:
```json
{
    "message": "success"
}
```

## Data Types

### Payment Types
- `1`: Cash
- `2`: Credit

### Order Status
- `1`: Pending (assumed)
- `2`: Confirmed/Processing
- `3`: Completed (assumed)

### User Roles
- `admin`: Administrator
- `worker`: Field worker
- `leader`: Team leader

## Notes

- All authenticated endpoints require a Bearer token in the Authorization header
- File uploads use `multipart/form-data` content type
- Orders support multiple products using array notation: `products[0]`, `products[1]`, etc.
- Location coordinates are stored as strings
- Some populated fields may return `null` if referenced data doesn't exist
- All timestamps are in ISO 8601 format
- The API uses MongoDB ObjectId format for all IDs