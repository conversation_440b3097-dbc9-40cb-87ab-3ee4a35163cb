{"app": {"name": "<PERSON><PERSON>", "version": "App Version"}, "nazoratchi": {"market_structure": {"total_places": "Total Places"}, "tuzilma": {"last_activity": {"title": "Last Activity", "loading": "Loading...", "no_data": "Data not loaded", "error_occurred": "Error occurred", "retry": "Retry", "just_now": "Just now", "minutes_ago": "{count} minutes ago", "hours_ago": "{count} hours ago", "days_ago": "{count} days ago"}, "errors": {"no_data": "No data available", "invalid_response_format": "Invalid response format", "parse_error": "Parse error", "api_error": "API error"}}}, "theme": {"title": "Select Theme", "light": "Light", "light_subtitle": "White interface", "dark": "Dark", "dark_subtitle": "Black interface", "system": "System", "system_subtitle": "Follow device settings"}, "dialogs": {"qr_payment": {"title": "Pay via Click", "place_number": "Place #{number}", "payment_details": "Payment Information", "square_number": "Square number:", "days_count": "Number of days:", "daily_price": "Daily price:", "total_payment": "Total payment:", "show_qr_code": "Show QR code", "cancel": "Cancel", "generating_qr": "Generating QR code...", "scan_instruction": "Scan the QR code\nto make payment", "check_payment": "Check payment", "retry": "Retry"}, "payment_status": {"pay_button": "Pay", "amount_format": "{quantity} days ({amount} UZS)", "check_number_copied": "Check number copied: {checkNumber}", "delete_confirmation_title": "Confirm", "delete_confirmation_message": "Do you want to delete this payment?", "delete_confirmation_no": "No", "delete_confirmation_yes": "Yes!"}}, "face_control": {"title": "Face Control System", "face_control_title": "Face Control", "enter_face": "Enter Face", "confirm_face": "Confirm Face", "settings": "Settings", "about": "About", "refresh": "Refresh", "please_refresh_page": "Please refresh the page", "warning_message": "Attention! For best results, follow these guidelines:", "warning_details": "• Be in a well-lit place\n• Only one face should be in the image\n• Remove glasses, masks and all accessories covering the face", "camera_preview_message": "Please check the captured image, correct orientation if necessary. Your face should be straight!", "uploaded": "Uploaded", "recognised": "Recognised", "face_match": "Face Match", "liveness": "Liveness", "analyzing": "Analyzing", "error_uploading": "Error uploading", "leader_not_confirmed": "Leader not confirmed", "picture_uploaded": "Picture uploaded", "errors": {"check_internet": "Check internet connection", "user_not_found": "User not found", "unknown_role": "Unknown user role: ", "invalid_user_data": "Invalid user data", "profile_load_error": "Profile loading error: ", "download_error": "Download error", "image_capture_error": "Image capture error: ", "image_process_error": "Image processing error: ", "logout_error": "<PERSON><PERSON><PERSON> error: ", "face_not_detected": "Face not detected"}, "settings_page": {"title": "Settings", "camera_lens": "Camera Lens", "front": "Front", "thresholds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "liveness_level": "Liveness Level", "liveness_threshold": "Liveness Threshold", "identify_threshold": "Identify Threshold", "reset": "Reset", "restore_default": "<PERSON><PERSON>", "clear_all_person": "Clear All Persons", "cancel": "Cancel", "ok": "OK"}, "about_page": {"title": "Premium Soft", "developer_message": "Hello! I'm the developer. If you have face control issues, contact me!", "email": "Email: <EMAIL>", "phone": "Phone: +998911283725", "telegram": "Telegram: @flutterblogs", "github": "Github: https://github.com/mamasodikov"}, "functional_page": {"unlock_app": "Unlock App", "support_info": "Support Information", "time_check": "Device time comparison with server", "location_check": "Location permission check", "mock_location_check": "Mock location check", "waiting": "Waiting...", "please_fix_issues": "Please fix the above issues...", "time_difference": "Server time difference: {Farq} minutes"}, "person_view": {"approved": "Approved", "not_approved": "Not Approved", "admin_only_delete": "Only admin can delete image.."}, "face_detection": {"face_control": "Face Control", "error_uploading": "Error uploading", "admin_not_approved": "Admin not approved", "uploaded": "Uploaded", "approved": "Approved", "photo_uploaded": "Photo uploaded", "similarity": "Similarity: {percent}%", "liveness": "Liveness: {percent}%", "liveness_calculating": "Similarity..", "upload_error_message": "Error: {message}"}, "license_errors": {"invalid_license": "Invalid license!", "license_expired": "License expired!", "not_activated": "Not activated!", "init_error": "Initialization error!"}, "face_messages": {"face_not_detected": "Face not detected", "face_added": "Face added", "multiple_faces": "Multiple faces detected"}, "person_management": {"all_persons_deleted": "All persons deleted!", "person_removed": "Person removed!"}, "permission_dialog": {"title": "Permissions", "message": "Grant camera and microphone permissions", "continue_button": "Continue"}}}