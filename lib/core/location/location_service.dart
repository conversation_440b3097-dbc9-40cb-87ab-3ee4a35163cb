import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_session_manager/flutter_session_manager.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get_storage/get_storage.dart';
import '../../translations/locale_keys.g.dart';

abstract class LocationService {
  Future<String> getLatLang();
}

class LocationServiceImpl extends LocationService {
  @override
  Future<String> getLatLang() async {
    LocationPermission permission;
    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.deniedForever) {
        return "0,0";
      }
    }
    final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high);

    return "${position.latitude},${position.longitude}";
  }
}

Future<Position> determinePosition([bool? isBackgroundService]) async {
  bool serviceEnabled;
  LocationPermission permission;
  var sm = SessionManager();
  final storage = GetStorage();
  serviceEnabled = await Geolocator.isLocationServiceEnabled();

  permission = await Geolocator.checkPermission();

  if (permission == LocationPermission.denied) {
    permission = await Geolocator.requestPermission();

    if (permission == LocationPermission.denied) {
      return Future.error("DENIED");
    }
  }

  if (permission == LocationPermission.deniedForever) {
    return Future.error("DENIED_FOREVER");
  }

  try {
    var location = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.bestForNavigation);

    if (location.isMocked) {
      return Future.error('MOCKED',
          StackTrace.fromString('${location.latitude},${location.longitude}'));
    } else {
      return location;
    }
  } on LocationServiceDisabledException catch (e) {
    return Future.error("LOCATION_OFF");
  } catch (e) {
    return Future.error("LOCATION_OFF");
  }
}

Future<String> turnLocationOn(GetStorage storage) async {
  return LocaleKeys.location_turn_on_location_message.tr();
}

Future<String> giveLocationPermission(GetStorage storage) async {
  return LocaleKeys.location_give_location_permission_message.tr();
}

Future<String> fraudLocation() async {
  return LocaleKeys.location_fraud_location_message.tr();
}
