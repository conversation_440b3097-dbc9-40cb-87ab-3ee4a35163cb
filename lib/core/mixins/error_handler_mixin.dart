import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import '../services/dio_error_handler.dart';

/// Mixin for BLoC classes to handle errors consistently
mixin ErrorHandlerMixin<Event, State> on Bloc<Event, State> {
  
  /// Execute API call with error handling for BLoC
  Future<void> executeApiCall<T>({
    required Future<T> Function() apiCall,
    required Function(T data) onSuccess,
    required Function(String message) onFailure,
    Function()? onLoading,
    bool checkNetwork = true,
  }) async {
    try {
      // Call loading state if provided
      onLoading?.call();
      
      // Check network connectivity if required
      if (checkNetwork && !await isNetworkConnected()) {
        onFailure('Internet aloqasi mavjud emas');
        return;
      }

      final result = await apiCall();
      onSuccess(result);
      
    } on DioException catch (e) {
      final errorMessage = DioErrorHandler.handleDioError(e);
      onFailure(errorMessage);
      
    } catch (e) {
      final errorMessage = DioErrorHandler.handleGenericError(e);
      onFailure(errorMessage);
    }
  }

  /// Execute multiple API calls in sequence
  Future<void> executeSequentialApiCalls({
    required List<Future<dynamic> Function()> apiCalls,
    required Function(List<dynamic> results) onSuccess,
    required Function(String message) onFailure,
    Function()? onLoading,
    bool stopOnFirstError = true,
  }) async {
    try {
      onLoading?.call();
      
      if (!await isNetworkConnected()) {
        onFailure('Internet aloqasi mavjud emas');
        return;
      }

      final List<dynamic> results = [];
      
      for (final apiCall in apiCalls) {
        try {
          final result = await apiCall();
          results.add(result);
        } catch (e) {
          if (stopOnFirstError) {
            final errorMessage = e is DioException 
                ? DioErrorHandler.handleDioError(e)
                : DioErrorHandler.handleGenericError(e);
            onFailure(errorMessage);
            return;
          } else {
            results.add(null); // Add null for failed calls
          }
        }
      }
      
      onSuccess(results);
      
    } catch (e) {
      final errorMessage = DioErrorHandler.handleGenericError(e);
      onFailure(errorMessage);
    }
  }

  /// Execute API calls in parallel
  Future<void> executeParallelApiCalls({
    required List<Future<dynamic> Function()> apiCalls,
    required Function(List<dynamic> results) onSuccess,
    required Function(String message) onFailure,
    Function()? onLoading,
  }) async {
    try {
      onLoading?.call();
      
      if (!await isNetworkConnected()) {
        onFailure('Internet aloqasi mavjud emas');
        return;
      }

      final futures = apiCalls.map((call) => call()).toList();
      final results = await Future.wait(futures, eagerError: false);
      
      onSuccess(results);
      
    } catch (e) {
      final errorMessage = e is DioException 
          ? DioErrorHandler.handleDioError(e)
          : DioErrorHandler.handleGenericError(e);
      onFailure(errorMessage);
    }
  }

  /// Handle pagination with error handling
  Future<void> executePaginatedApiCall<T>({
    required Future<T> Function(int page) apiCall,
    required int page,
    required Function(T data) onSuccess,
    required Function(String message) onFailure,
    Function()? onLoading,
  }) async {
    await executeApiCall(
      apiCall: () => apiCall(page),
      onSuccess: onSuccess,
      onFailure: onFailure,
      onLoading: onLoading,
    );
  }

  /// Check network connectivity
  /// Override this method to implement actual network checking
  Future<bool> isNetworkConnected() async {
    // Implement network connectivity check here
    // For now, return true
    return true;
  }

  /// Handle authentication errors
  void handleAuthError() {
    // Implement logout logic or navigation to login
    // This can be overridden in specific BLoCs
  }

  /// Handle validation errors
  Map<String, String> extractValidationErrors(DioException error) {
    final validationErrors = DioErrorHandler.extractValidationErrors(error);
    final Map<String, String> simpleErrors = {};
    
    validationErrors.forEach((field, messages) {
      if (messages.isNotEmpty) {
        simpleErrors[field] = messages.first;
      }
    });
    
    return simpleErrors;
  }

  /// Check if error requires retry
  bool shouldRetryError(dynamic error) {
    return DioErrorHandler.isNetworkError(error);
  }

  /// Execute with retry logic
  Future<void> executeWithRetry<T>({
    required Future<T> Function() apiCall,
    required Function(T data) onSuccess,
    required Function(String message) onFailure,
    Function()? onLoading,
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 1),
  }) async {
    int attempts = 0;
    
    while (attempts <= maxRetries) {
      try {
        if (attempts == 0) onLoading?.call();
        
        if (!await isNetworkConnected()) {
          onFailure('Internet aloqasi mavjud emas');
          return;
        }

        final result = await apiCall();
        onSuccess(result);
        return;
        
      } catch (e) {
        attempts++;
        
        if (attempts > maxRetries || !shouldRetryError(e)) {
          final errorMessage = e is DioException 
              ? DioErrorHandler.handleDioError(e)
              : DioErrorHandler.handleGenericError(e);
          onFailure(errorMessage);
          return;
        }
        
        if (attempts <= maxRetries) {
          await Future.delayed(retryDelay * attempts);
        }
      }
    }
  }
}
