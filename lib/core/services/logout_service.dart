import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../theme/theme_manager.dart';
import '../utils/app_constants.dart';
import 'language_service.dart';

/// Universal logout service for handling user logout across the app
class LogoutService {
  final GetStorage _storage;
  final SharedPreferences prefs;

  LogoutService( {required GetStorage storage, required this.prefs}) : _storage = storage;

  /// Perform complete logout by clearing all stored data while preserving language
  Future<void> performLogout({
    bool showMessage = false,
    BuildContext? context,
  }) async {

    final themeManager = ThemeManager();
    try {

      print('🚪 Starting logout process...');

      // Save current language setting before clearing data
      final currentLanguage = _storage.read('selected_language');

      // Clear authentication tokens
      await _storage.remove(TOKEN);
      await _storage.remove(REFRESH_TOKEN);

      // Clear user data

      await _storage.remove(USER_ID);
      await _storage.remove(USER_PROFILE);
      themeManager.clearTheme();

      // Restore language setting (default to 'uz' if none was saved)
      final languageToRestore = currentLanguage ?? 'uz';
      await _storage.write('selected_language', languageToRestore);

      // Update app locale if context is available
      if (context != null) {
        if (context.mounted) {
          await LanguageService.setLanguage(context, languageToRestore);
        }
      }

      print('✅ Logout completed successfully, language preserved: $languageToRestore');

      // Show message if requested
      if (showMessage && context != null) {
        if (context.mounted) {
          _showLogoutMessage(context);
        }
      }

    } catch (e) {
      print('❌ Error during logout: $e');
      // Even if there's an error, try to clear critical data
      await _storage.remove(TOKEN);
      await _storage.remove(REFRESH_TOKEN);

      // Ensure language is set to default even in error case
      try {
        await _storage.write('selected_language', 'uz');
        if (context != null) {
          if (context.mounted) {
            await LanguageService.setLanguage(context, 'uz');
          }
        }
        print('🌐 Language set to default (uz) after error');
      } catch (langError) {
        print('⚠️ Error setting default language: $langError');
      }
    }
  }

  /// Completely erase all GetStorage data while preserving language setting
  Future<void> _eraseAllStorage({BuildContext? context}) async {
    try {

      print('🧹 Erasing all GetStorage data while preserving language...');

      // Save current language setting before erasing
      final currentLanguage = _storage.read('selected_language');

      // Erase all data
      await _storage.erase();
      await prefs.clear();

      //Clear theme data
      final themeManager = ThemeManager();
      themeManager.clearTheme();

      // Restore language setting (default to 'uz' if none was saved)
      final languageToRestore = currentLanguage ?? 'uz';
      await _storage.write('selected_language', languageToRestore);

      // Update app locale if context is available
      if (context != null) {
        if (context.mounted) {
          await LanguageService.setLanguage(context, languageToRestore);
        }
      }

      print('✅ All GetStorage data erased successfully, language preserved: $languageToRestore');
    } catch (e) {
      print('❌ Error erasing GetStorage: $e');
      // Fallback: try to clear known keys individually
      await _clearKnownKeys();

      // Ensure language is set to default even in fallback
      try {
        await _storage.write('selected_language', 'uz');
        if (context != null) {
          if (context.mounted) {
            await LanguageService.setLanguage(context, 'uz');
          }
        }
        print('🌐 Language set to default (uz) after fallback');
      } catch (langError) {
        print('⚠️ Error setting default language: $langError');
      }
    }
  }

  /// Clear all known storage keys individually (fallback method)
  Future<void> _clearKnownKeys() async {
    try {
      final knownKeys = [
        TOKEN,
        REFRESH_TOKEN,
        USER_ID,
        USER_PROFILE,
        'cached_user_profile',
        'profile_cache_time',
        'sot_user_profile',
        'sot_profile_cache_time',
        APP_VERSION,
      ];

      for (final key in knownKeys) {
        await _storage.remove(key);
      }
      print('🗑️ Known keys cleared as fallback');
    } catch (e) {
      print('⚠️ Error clearing known keys: $e');
    }
  }

  /// Show logout message to user
  void _showLogoutMessage(BuildContext context) {
    if (!context.mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Hisobdan chiqildi'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  /// Complete logout with full storage erasure (most comprehensive)
  Future<void> performCompleteLogout({
    bool showMessage = false,
    BuildContext? context,
  }) async {
    try {
      print('🚪 Starting complete logout with storage erasure...');

      // Erase all GetStorage data while preserving language
      await _eraseAllStorage(context: context);

      print('✅ Complete logout finished successfully');

      // Show message if requested
      if (showMessage && context != null) {
        if (context.mounted) {
          _showLogoutMessage(context);
        }
      }

    } catch (e) {
      print('❌ Error during complete logout: $e');
      // Fallback to regular logout
      if (context != null) {
        await performLogout(showMessage: false, context: context);
      }
    }
  }
}
