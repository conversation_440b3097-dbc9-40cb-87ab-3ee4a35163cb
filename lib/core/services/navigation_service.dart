import 'package:flutter/material.dart';
import '../../features/auth/presentation/pages/login_page.dart';

/// Global navigation service for handling navigation without context
class NavigationService {
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  /// Get the current navigator state
  NavigatorState? get navigator => navigatorKey.currentState;

  /// Get the current context
  BuildContext? get context => navigatorKey.currentContext;

  /// Navigate to login page and clear all routes (for logout)
  Future<void> navigateToLoginAndClearStack() async {
    final navigator = this.navigator;
    if (navigator != null) {
      print('🔄 Navigating to login page and clearing navigation stack...');
      
      // Navigate to login page and remove all previous routes
      navigator.pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const LoginPage()),
        (route) => false,
      );
      
      print('✅ Navigation to login completed');
    } else {
      print('❌ Navigator not available - cannot navigate to login');
    }
  }

  /// Navigate to a specific page
  Future<void> navigateTo(Widget page) async {
    final navigator = this.navigator;
    if (navigator != null) {
      navigator.push(MaterialPageRoute(builder: (context) => page));
    }
  }

  /// Navigate and replace current page
  Future<void> navigateAndReplace(Widget page) async {
    final navigator = this.navigator;
    if (navigator != null) {
      navigator.pushReplacement(MaterialPageRoute(builder: (context) => page));
    }
  }

  /// Pop current page
  void pop([dynamic result]) {
    final navigator = this.navigator;
    if (navigator != null && navigator.canPop()) {
      navigator.pop(result);
    }
  }

  /// Check if can pop
  bool canPop() {
    final navigator = this.navigator;
    return navigator?.canPop() ?? false;
  }
}
