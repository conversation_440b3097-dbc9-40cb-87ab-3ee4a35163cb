import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:get_storage/get_storage.dart';
import 'app_theme.dart';

/// Theme manager for handling theme changes and state
/// This class manages the current theme mode and provides methods to switch themes
class ThemeManager extends ChangeNotifier {
  static final ThemeManager _instance = ThemeManager._internal();
  factory ThemeManager() => _instance;
  ThemeManager._internal() {
    _loadThemeFromStorage();
  }

  final GetStorage _storage = GetStorage();
  static const String _themeKey = 'app_theme_mode';

  // Current theme mode (dark by default)
  ThemeMode _themeMode = ThemeMode.light;

  /// Get the current theme mode
  ThemeMode get themeMode => _themeMode;

  /// Get the current theme data based on theme mode
  ThemeData get currentTheme {
    switch (_themeMode) {
      case ThemeMode.light:
        return AppTheme.lightTheme;
      case ThemeMode.dark:
        return AppTheme.darkTheme;
      case ThemeMode.system:
        // Check system brightness and return appropriate theme
        return _getSystemTheme();
    }
  }

  /// Get theme based on system brightness
  ThemeData _getSystemTheme() {
    final brightness = SchedulerBinding.instance.platformDispatcher.platformBrightness;
    return brightness == Brightness.dark ? AppTheme.darkTheme : AppTheme.lightTheme;
  }

  /// Check if current theme is dark
  bool get isDarkMode => _themeMode == ThemeMode.dark;

  /// Check if current theme is light
  bool get isLightMode => _themeMode == ThemeMode.light;

  /// Check if current theme follows system
  bool get isSystemMode => _themeMode == ThemeMode.system;

  /// Check if the effective theme is dark (considering system theme)
  bool get isEffectiveDarkMode {
    switch (_themeMode) {
      case ThemeMode.dark:
        return true;
      case ThemeMode.light:
        return false;
      case ThemeMode.system:
        final brightness = SchedulerBinding.instance.platformDispatcher.platformBrightness;
        return brightness == Brightness.dark;
    }
  }

  /// Check if the effective theme is light (considering system theme)
  bool get isEffectiveLightMode => !isEffectiveDarkMode;

  /// Load theme from storage
  void _loadThemeFromStorage() {
    final savedTheme = _storage.read(_themeKey);
    if (savedTheme != null) {
      switch (savedTheme) {
        case 'light':
          _themeMode = ThemeMode.light;
          break;
        case 'dark':
          _themeMode = ThemeMode.dark;
          break;
        case 'system':
          _themeMode = ThemeMode.system;
          break;
        default:
          _themeMode = ThemeMode.dark;
      }
    }
  }

  /// Save theme to storage
  void _saveThemeToStorage() {
    _storage.write(_themeKey, _themeMode.name);
  }

  /// Remove theme from storage
  void _clearThemeFromStorage() {
    _storage.remove(_themeKey);
  }

  /// Set theme mode to dark
  void setDarkMode() {
    _themeMode = ThemeMode.dark;
    _saveThemeToStorage();
    notifyListeners();
  }

  /// Set theme mode to light
  void setLightMode() {
    _themeMode = ThemeMode.light;
    _saveThemeToStorage();
    notifyListeners();
  }

  void clearTheme() {
    _clearThemeFromStorage();
    notifyListeners();
  }

  /// Set theme mode to follow system
  void setSystemMode() {
    _themeMode = ThemeMode.system;
    _saveThemeToStorage();
    notifyListeners();
  }

  /// Toggle between light and dark mode
  void toggleTheme() {
    if (_themeMode == ThemeMode.dark) {
      setLightMode();
    } else {
      setDarkMode();
    }
  }

  /// Set theme mode directly
  void setThemeMode(ThemeMode mode) {
    _themeMode = mode;
    _saveThemeToStorage();
    notifyListeners();
  }

  /// Get theme mode string representation
  String get themeModeString {
    switch (_themeMode) {
      case ThemeMode.light:
        return 'Light';
      case ThemeMode.dark:
        return 'Dark';
      case ThemeMode.system:
        return 'System';
    }
  }

  /// Get theme mode icon
  IconData get themeModeIcon {
    switch (_themeMode) {
      case ThemeMode.light:
        return Icons.light_mode;
      case ThemeMode.dark:
        return Icons.dark_mode;
      case ThemeMode.system:
        return Icons.brightness_auto;
    }
  }
}