// All sizes
const double cRadius8 = 8.0;
const double cRadius10 = 10.0;
const double cRadius12 = 12.0;
const double cRadius16 = 16.0;
const double cRadius22 = 22.0;
const double cRadius36 = 36.0;

const double HEADER_SIZE = 120;
const double DESIGN_WIDTH = 375;
const double DESIGN_HEIGHT = 812;
const double HEADER_SIZE_SUB = 40;

// Variables
String APP_VERSION = "app_version";

//Contacts
const String TOKEN = 'token';
const String REFRESH_TOKEN = 'refreshToken';
const String USER_ID = 'user_id';
const String USER_PROFILE = 'user_profile';
const String FIRST_NAME = 'first_name';
const String LAST_NAME = 'last_name';
const String MIDDLE_NAME = 'middle_name';
const String PHONE = 'phone';

// Environment constants
const String baseUrlPref = "base_url_pref";
const String language_pref = "language_pref";

class AppStrings {
  static const strNoRouteFound = "no_route_found";
  static const strAppName = "app_name";

  static const String success = "success";

  // error handler
  static const String strBadRequestError = "bad_request_error";
  static const String strNoContent = "no_content";
  static const String strForbiddenError = "forbidden_error";
  static const String strUnauthorizedError = "unauthorized_error";
  static const String strNotFoundError = "not_found_error";
  static const String strConflictError = "conflict_error";
  static const String strInternalServerError = "internal_server_error";
  static const String strUnknownError = "unknown_error";
  static const String strTimeoutError = "timeout_error";
  static const String strDefaultError = "default_error";
  static const String strCacheError = "cache_error";
  static const String strNoInternetError = "no_internet_error";
}
