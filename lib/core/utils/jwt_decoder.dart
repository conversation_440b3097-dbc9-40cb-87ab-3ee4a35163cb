import 'dart:convert';

/// JWT token decoder utility
class JwtDecoder {
  /// Decode JWT token and extract payload
  static Map<String, dynamic>? decodeToken(String token) {
    try {
      // Split the token into parts
      final parts = token.split('.');
      if (parts.length != 3) {
        return null;
      }

      // Get the payload part (second part)
      String payload = parts[1];
      
      // Add padding if needed for base64 decoding
      switch (payload.length % 4) {
        case 0:
          break;
        case 2:
          payload += '==';
          break;
        case 3:
          payload += '=';
          break;
        default:
          return null;
      }

      // Decode base64
      final bytes = base64Url.decode(payload);
      final jsonString = utf8.decode(bytes);
      
      // Parse JSON
      return json.decode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      print('Error decoding JWT token: $e');
      return null;
    }
  }

  /// Check if token is expired
  static bool isTokenExpired(String token) {
    final payload = decodeToken(token);
    if (payload == null) return true;

    final exp = payload['exp'];
    if (exp == null) return true;

    final expirationDate = DateTime.fromMillisecondsSinceEpoch(exp * 1000);
    return DateTime.now().isAfter(expirationDate);
  }



  /// Extract user ID from token
  static String? getUserId(String token) {
    final payload = decodeToken(token);
    return payload?['_id'] as String?;
  }

  /// Extract user first name from token
  static String? getFirstName(String token) {
    final payload = decodeToken(token);
    return payload?['firstName'] as String?;
  }

  /// Extract user last name from token
  static String? getLastName(String token) {
    final payload = decodeToken(token);
    return payload?['lastName'] as String?;
  }

  /// Extract user middle name from token
  static String? getMiddleName(String token) {
    final payload = decodeToken(token);
    return payload?['middleName'] as String?;
  }

  /// Extract user phone from token
  static String? getPhone(String token) {
    final payload = decodeToken(token);
    return payload?['phone'] as String?;
  }

  /// Extract user role from token
  static String? getRole(String token) {
    final payload = decodeToken(token);
    return payload?['role'] as String?;
  }

  /// Get full name from token
  static String getFullName(String token) {
    final firstName = getFirstName(token) ?? '';
    final lastName = getLastName(token) ?? '';
    final middleName = getMiddleName(token) ?? '';

    final parts = [firstName, lastName, middleName].where((part) => part.isNotEmpty);
    return parts.join(' ').trim();
  }
}
