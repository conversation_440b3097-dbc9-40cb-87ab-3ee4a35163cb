import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../translations/locale_keys.g.dart';
import '../theme/app_colors.dart';

class CustomToast {
  static void showToast(String text) {
    Fluttertoast.showToast(
      msg: text,
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.TOP,
      timeInSecForIosWeb: 1,
      backgroundColor: Colors.black38,
      textColor: Colors.white,
      fontSize: 16.0,
    );
  }

  /// Show error toast with red background
  /// Note: Fluttertoast doesn't have access to BuildContext, so we use hardcoded error color
  /// For theme-aware error messages, use AppFunctions.showErrorSnackBar instead
  static void showErrorToast(String text) {
    Fluttertoast.showToast(
      msg: text,
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.TOP,
      timeInSecForIosWeb: 1,
      backgroundColor: AppColors.cReddishColor, // Keep hardcoded as Fluttertoast doesn't support theming
      textColor: Colors.white,
      fontSize: 16.0,
    );
  }
}

/// Legacy SnackBar function - use AppFunctions.showSnackBar instead for better theming
Snack(String msg, BuildContext ctx, Color color) {
  var snackBar = SnackBar(
      backgroundColor: color,
      content: Text(
        msg,
        textAlign: TextAlign.center,
        style: TextStyle(color: Theme.of(ctx).colorScheme.onSurface),
      ));
  ScaffoldMessenger.of(ctx).showSnackBar(snackBar);
}

/// Legacy SnackBar function with action - use AppFunctions.showSnackBar instead for better theming
SnackAction(String msg, BuildContext ctx, Color color, Function fnc) {
  var snackBar = SnackBar(
      duration: Duration(seconds: 5),
      action: SnackBarAction(
        label: LocaleKeys.common_clear.tr(),
        textColor: Theme.of(ctx).colorScheme.primary,
        onPressed: () {
          fnc();
        },
      ),
      backgroundColor: color,
      content: Text(
        msg,
        style: TextStyle(
            color: Theme.of(ctx).colorScheme.onSurface,
            fontSize: 15.sp,
            fontWeight: FontWeight.bold),
        textAlign: TextAlign.center,
      ));
  ScaffoldMessenger.of(ctx).showSnackBar(snackBar);
}
