import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import '../../generated/assets.dart';
import '../../translations/locale_keys.g.dart';
import '../theme/app_colors.dart';
import '../theme/app_text_styles.dart';
class RestScheduleDatePicker extends StatefulWidget {
  final DateTime selectedDate;
  final Function(DateTime) onDateSelected;

  const RestScheduleDatePicker({
    super.key,
    required this.selectedDate,
    required this.onDateSelected,
  });

  @override
  State<RestScheduleDatePicker> createState() => _RestScheduleDatePickerState();
}

class _RestScheduleDatePickerState extends State<RestScheduleDatePicker> {
  DateFormat dateFormat = DateFormat('yyyy-MM-dd');

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: GestureDetector(
        onTap: () => _selectDate(context),
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                dateFormat.format(widget.selectedDate),
                style: AppTextStyles.bodyMedium.copyWith(
                  color: Theme.of(context).colorScheme.onSurface,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
              SvgPicture.asset(
                Assets.iconsCalendar,
                colorFilter: ColorFilter.mode(
                  Theme.of(context).colorScheme.onSurface,
                  BlendMode.srcIn,
                ),
                width: 24,
                height: 24,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    DateTime tempSelectedDate = widget.selectedDate;
    DateTime originalDate =
        widget.selectedDate; // Store original date for cancel

    final bool? result = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Theme(
              data: Theme.of(context).copyWith(
                colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary: Theme.of(context).colorScheme.primary,
                  onPrimary: Theme.of(context).colorScheme.onPrimary,
                  surface: Theme.of(context).colorScheme.surface,
                  onSurface: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              child: AlertDialog(
                backgroundColor: Theme.of(context).colorScheme.surface,
                title: Text(
                  LocaleKeys.date_picker_dialog_title.tr(),
                  style: TextStyle(color: Theme.of(context).colorScheme.primary),
                ),
                contentPadding: EdgeInsets.fromLTRB(0, 20, 0, 0),
                content: SizedBox(
                  width: 300,
                  height: 400,
                  child: CalendarDatePicker(
                    initialDate: tempSelectedDate,
                    firstDate: DateTime(2020),
                    lastDate: DateTime.now().add(const Duration(days: 365)),
                    onDateChanged: (DateTime date) {
                      setState(() {
                        tempSelectedDate = date;
                      });
                      // Send date immediately when selected and close dialog
                      widget.onDateSelected(date);
                      Navigator.of(context)
                          .pop(true); // Close dialog immediately
                    },
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      // Restore original date if cancelled
                      Navigator.of(context).pop(true); // Cancel
                    },
                    child: Text(
                      LocaleKeys.date_picker_cancel.tr(),
                      style: TextStyle(color: Theme.of(context).colorScheme.onSurfaceVariant),
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      // Confirm the current selection (already sent immediately)
                      widget.onDateSelected(originalDate);
                      Navigator.of(context).pop(false); // OK
                    },
                    child: Text(
                      'OK',
                      style: TextStyle(color: AppColors.cFirstColor),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );

    // If user cancelled, ensure original date is restored
    if (result == false) {
      widget.onDateSelected(originalDate);
    }
  }
}
