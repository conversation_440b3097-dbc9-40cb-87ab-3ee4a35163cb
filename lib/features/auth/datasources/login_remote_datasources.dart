import 'package:auto_sms_verification/auto_sms_verification.dart';
import 'package:mobil_kochat/core/utils/jwt_decoder.dart';
import '../../../core/network/network_info.dart';
import '../../../core/utils/api_path.dart';

import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import '../models/auth_models.dart';
import '../../../translations/locale_keys.g.dart';

class LoginRemoteDatasourceImpl {
  final NetworkInfo networkInfo;
  final Dio dio;

  LoginRemoteDatasourceImpl({
    required this.networkInfo,
    required this.dio,
  });

  Future<Map<String, dynamic>> loginWithPassword(
      String phone,String password) async {
    // Check network connectivity first
    if (!await networkInfo.isConnected) {
      throw DioException(
        requestOptions: RequestOptions(
          path: ApiPath.loginPath,
        ),
        type: DioExceptionType.connectionError,
        message: LocaleKeys.common_no_internet_connection.tr(),
      );
    }
    try {
      var response = await dio.post(
        ApiPath.loginPath,
        data: {
          'phone': phone,
          'password': password
        },
      );
      final data = response.data;
      if (response.statusCode == 200) {
        //Get token from response
        if (data.containsKey('token')) {
          final token = data['token'] as String?;

          if (token != null) {
            // Decode token to extract user info
            final userId = JwtDecoder.getUserId(token);

            final result = {
              'success': true,
              'token': token,
              'userId': userId,
              'message': 'Muvaffaqiyatli kirish',
            };

            return result;
          }
          return {
            'success': false,
            'message': data['message'] ?? 'Token not found',
          };
        }
      }
      return {
        'success': false,
        'message': 'Server xatoligi',
      };
    } on DioException catch (e) {
      print('DioException: $e');
      // Re-throw DioException to be handled by the error handler
      rethrow;
    } catch (e) {
      // Convert any other exception to DioException for consistent handling
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.loginPath),
        type: DioExceptionType.unknown,
        error: e,
      );
    }
  }

  Future<bool> login(
      String phone, String appSignature) async {
    // Check network connectivity first
    if (!await networkInfo.isConnected) {
      throw DioException(
        requestOptions: RequestOptions(
          path: ApiPath.loginPath,
        ),
        type: DioExceptionType.connectionError,
        message: LocaleKeys.common_no_internet_connection.tr(),
      );
    }
    try {
      var response = await dio.post(
        ApiPath.loginPath,
        data: {
          'phone': phone,
          'appSignature': appSignature,
        },
      );
      final data = response.data;
      if (response.statusCode == 200) {
        if (data.containsKey('message')) {
          return data['message'] == 'success';
        }
        return false;
      }
      return false;
    } on DioException catch (e) {
      // Re-throw DioException to be handled by the error handler
      rethrow;
    } catch (e) {
      // Convert any other exception to DioException for consistent handling
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.loginPath),
        type: DioExceptionType.unknown,
        error: e,
      );
    }
  }

  /// Send SMS code for resend functionality using real API
  Future<AuthResponse> resendSmsCode(
      String phoneNumber) async {
    try {
      // Check network connectivity first
      if (!await networkInfo.isConnected) {
        return AuthResponse(
          success: false,
          message: LocaleKeys.auth_services_no_internet.tr(),
        );
      }

      // Extract phone number without formatting for API call
      String cleanPhoneNumber =
      phoneNumber.trim().replaceAll('+998 ', '').replaceAll(' ', '');

      // Get app signature for Android
      String appSignature = 'undefined';
      try {
        appSignature = await AutoSmsVerification.appSignature() ?? 'undefined';
      } catch (e) {
        // Ignore error and use default signature
        appSignature = 'undefined';
      }

      // Use the same API endpoints as login flow
      final response = await dio.post(
        ApiPath.loginPath,
        data: {
          'phone': cleanPhoneNumber,
          'appSignature': appSignature,
        },
      );

      final data = response.data;
      if (response.statusCode == 200) {
        if (data.containsKey('message') && data['message'] == 'success') {
          return AuthResponse(
            success: true,
            message: LocaleKeys.auth_services_sms_resent.tr(),
          );
        } else {
          return AuthResponse(
            success: false,
            message: data['message'] ?? LocaleKeys.auth_services_sms_send_error.tr(),
          );
        }
      } else {
        return AuthResponse(
          success: false,
          message: LocaleKeys.auth_services_server_error.tr(),
        );
      }
    } on DioException catch (e) {
      // Handle Dio exceptions
      String errorMessage = LocaleKeys.auth_services_general_error.tr();
      if (e.type == DioExceptionType.connectionTimeout) {
        errorMessage = LocaleKeys.auth_services_connection_timeout.tr();
      } else if (e.type == DioExceptionType.receiveTimeout) {
        errorMessage = LocaleKeys.auth_services_receive_timeout.tr();
      } else if (e.type == DioExceptionType.connectionError) {
        errorMessage = LocaleKeys.auth_services_connection_error.tr();
      } else if (e.response?.statusCode == 400) {
        errorMessage = LocaleKeys.auth_services_bad_request.tr();
      } else if (e.response?.statusCode == 500) {
        errorMessage = LocaleKeys.auth_services_server_error.tr();
      }

      return AuthResponse(
        success: false,
        message: errorMessage,
      );
    } catch (e) {
      return AuthResponse(
        success: false,
        message: LocaleKeys.auth_services_unexpected_error.tr(),
      );
    }
  }
}
