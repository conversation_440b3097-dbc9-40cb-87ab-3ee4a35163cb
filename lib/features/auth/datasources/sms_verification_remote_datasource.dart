import '../../../core/network/network_info.dart';
import '../../../core/utils/api_path.dart';
import '../../../core/utils/jwt_decoder.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';

class SmsVerificationRemoteDatasourceImpl {
  final NetworkInfo networkInfo;
  final Dio dio;

  SmsVerificationRemoteDatasourceImpl({
    required this.networkInfo,
    required this.dio,
  });

  Future<Map<String, dynamic>> verifySmsCode(String phone, String verifyCode) async {
    // Check network connectivity first
    if (!await networkInfo.isConnected) {
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.authPath),
        type: DioExceptionType.connectionError,
        message: 'Internet aloqasi mavjud emas',
      );
    }

    try {
      var response = await dio.post(
        ApiPath.authPath,
        data: {
          'phone': phone,
          'verifyCode': int.parse(verifyCode),
        },
      );

      final data = response.data;
      if (response.statusCode == 200) {
        print("SMS Verification Data: $data");

        if (data.containsKey('message') && data['message'] == 'success') {
          final token = data['token'] as String?;
          final refreshToken = data['refreshToken'] as String?; // Check for refresh token

          if (token != null) {
            // Decode token to extract user info
            final userId = JwtDecoder.getUserId(token);

            final result = {
              'success': true,
              'token': token,
              'userId': userId,
              'message': 'Muvaffaqiyatli kirish',
            };

            // Include refresh token if available
            if (refreshToken != null) {
              result['refreshToken'] = refreshToken;
            }

            return result;
          }
        }

        return {
          'success': false,
          'message': data['message'] ?? 'Noto\'g\'ri SMS kod',
        };
      }

      return {
        'success': false,
        'message': 'Server xatoligi',
      };
    } on DioException catch (e) {
      // Re-throw DioException to be handled by the error handler
      rethrow;
    } catch (e) {
      // Convert any other exception to DioException for consistent handling
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.authPath),
        type: DioExceptionType.unknown,
        error: e,
      );
    }
  }
}
