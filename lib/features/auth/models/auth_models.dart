

/// Authentication related data models
class LoginRequest {
  final String phoneNumber;
  final bool agreeToTerms;

  const LoginRequest({
    required this.phoneNumber,
    required this.agreeToTerms,
  });

  Map<String, dynamic> toJson() {
    return {
      'phone_number': phoneNumber,
      'agree_to_terms': agreeToTerms,
    };
  }
}

class SmsVerificationRequest {
  final String phoneNumber;
  final String smsCode;

  const SmsVerificationRequest({
    required this.phoneNumber,
    required this.smsCode,
  });

  Map<String, dynamic> toJson() {
    return {
      'phone_number': phoneNumber,
      'sms_code': smsCode,
    };
  }
}

class AuthResponse {
  final bool success;
  final String? token;
  final String? refreshToken;
  final String? message;

  const AuthResponse({
    required this.success,
    this.token,
    this.refreshToken,
    this.message,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    return AuthResponse(
      success: json['success'] ?? false,
      token: json['token'],
      refreshToken: json['refresh_token'],
      message: json['message'],
    );
  }
}

