import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'user_profile_model.g.dart';

@JsonSerializable()
class UserProfile extends Equatable {
  @Json<PERSON>ey(name: '_id')
  final String? id;
  
  final String? firstName;
  final String? lastName;
  final String? middleName;
  final String? phone;
  final String? password;
  final String? role;
  final bool? active;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const UserProfile({
    this.id,
    this.firstName,
    this.lastName,
    this.middleName,
    this.phone,
    this.password,
    this.role,
    this.active,
    this.createdAt,
    this.updatedAt,
  });

  /// Get full name combining first, last, and middle names
  String get fullName {
    final parts = [firstName, lastName, middleName]
        .where((part) => part != null && part.isNotEmpty);
    return parts.join(' ').trim();
  }

  /// Get display name (fallback to phone if no name available)
  String get displayName {
    final name = fullName;
    if (name.isNotEmpty) return name;
    if (phone != null && phone!.isNotEmpty) return phone!;
    return 'Foydalanuvchi';
  }

  /// Get formatted phone number
  String get formattedPhone {
    if (phone == null || phone!.isEmpty) return '';
    
    // If phone doesn't start with +998, add it
    String cleanPhone = phone!.replaceAll(RegExp(r'[^\d]'), '');
    if (cleanPhone.length == 9) {
      cleanPhone = '998$cleanPhone';
    }
    
    if (cleanPhone.startsWith('998') && cleanPhone.length == 12) {
      return '+${cleanPhone.substring(0, 3)} ${cleanPhone.substring(3, 5)} ${cleanPhone.substring(5, 8)} ${cleanPhone.substring(8, 10)} ${cleanPhone.substring(10)}';
    }
    
    return phone!;
  }

  /// Check if user is a worker
  bool get isWorker => role == 'worker';

  /// Check if user is active
  bool get isActive => active == true;

  factory UserProfile.fromJson(Map<String, dynamic> json) => _$UserProfileFromJson(json);
  
  Map<String, dynamic> toJson() => _$UserProfileToJson(this);

  UserProfile copyWith({
    String? id,
    String? firstName,
    String? lastName,
    String? middleName,
    String? phone,
    String? password,
    String? role,
    bool? active,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserProfile(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      middleName: middleName ?? this.middleName,
      phone: phone ?? this.phone,
      password: password ?? this.password,
      role: role ?? this.role,
      active: active ?? this.active,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        firstName,
        lastName,
        middleName,
        phone,
        password,
        role,
        active,
        createdAt,
        updatedAt,
      ];

  @override
  String toString() {
    return 'UserProfile(id: $id, fullName: $fullName, phone: $phone, role: $role, active: $active)';
  }

  /// Create UserProfile from JWT token
  static UserProfile fromToken(String token) {
    // This would extract data from JWT token
    // For now, we'll return empty profile as JWT might not contain all data
    return const UserProfile();
  }

  /// Create empty profile
  static const UserProfile empty = UserProfile();

  /// Check if profile is empty
  bool get isEmpty => id == null || id!.isEmpty;
}
