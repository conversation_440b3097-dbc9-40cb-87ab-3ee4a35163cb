import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:get_storage/get_storage.dart';
import 'package:mobil_kochat/features/auth/datasources/login_remote_datasources.dart';
import '../../../../../core/mixins/error_handler_mixin.dart';
import '../../../../../core/network/network_info.dart';
import '../../../../../core/services/simple_error_handler.dart';
import 'package:equatable/equatable.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../../../../core/utils/app_constants.dart';
import '../../../../../translations/locale_keys.g.dart';

part 'login_event.dart';

part 'login_state.dart';

class LoginBloc extends Bloc<LoginEvent, LoginState>
    with ErrorHandlerMixin<LoginEvent, LoginState> {
  final NetworkInfo networkInfo;
  final LoginRemoteDatasourceImpl loginRemoteDatasource;
  final GetStorage storage;

  LoginBloc(
      {required this.storage,
        required this.networkInfo,
        required this.loginRemoteDatasource})
      : super(LoginState()) {
    on<LoginPhoneEvent>(_handleLoginWithMixin);
    on<LoginWithPasswordEvent>(_handleLoginWithPassword);
  }

  Future<void> _handleLoginWithMixin(
      LoginPhoneEvent event, Emitter<LoginState> emit) async {
    await executeApiCall<bool>(
      apiCall: () =>
          loginRemoteDatasource.login(event.phone, event.appSignature),
      onLoading: () => emit(LoginState(status: LoginStatus.loading)),
      onSuccess: (response) {
        if (response) {
          emit(LoginState(status: LoginStatus.success));
        } else {
          emit(LoginState(
              status: LoginStatus.failure,
              message: LocaleKeys.auth_login_login_failed.tr()));
        }
      },
      onFailure: (message) =>
          emit(LoginState(status: LoginStatus.failure, message: message)),
    );
  }

  // COMMENTED OUT - Original login method for reference
  // Future<void> _handleLogin(LoginEvent event, Emitter<LoginState> emit) async {
  //   emit(LoginState(status: LoginStatus.loading));
  //
  //   try {
  //     var response = await loginRemoteDatasource.login(
  //         event.phone, event.appSignature, event.userRole);
  //     print("Result:${response}");
  //     if (response) {
  //       emit(LoginState(status: LoginStatus.success));
  //     } else {
  //       emit(LoginState(
  //           status: LoginStatus.failure,
  //           message:
  //           LocaleKeys.auth_login_login_failed.tr()));
  //     }
  //   } on DioException catch (e) {
  //     // Use universal Dio error handling
  //     final errorMessage = SimpleErrorHandler.handleError(e);
  //     emit(LoginState(status: LoginStatus.failure, message: errorMessage));
  //
  //     // Handle specific cases
  //     if (SimpleErrorHandler.requiresReauth(e)) {
  //       // Handle logout if needed
  //       // You can add logout logic here
  //     }
  //   } catch (e) {
  //     // Handle any other exceptions
  //     final errorMessage = SimpleErrorHandler.handleError(e);
  //     emit(LoginState(status: LoginStatus.failure, message: errorMessage));
  //   }
  // }

  // Handle login with password
  Future<void> _handleLoginWithPassword(
      LoginWithPasswordEvent event, Emitter<LoginState> emit) async {
    emit(LoginState(status: LoginStatus.loading));

    try {
      final response = await loginRemoteDatasource.loginWithPassword(event.phone, event.password);

      if (response['success'] == true) {
        print("Login with Password Result: $response");

        try {
          // Store token and user info in GetStorage
          final token = response['token'] as String?;
          final userId = response['userId'] as String?;
          final phone = response['phone'] as String?;
          final firstName = response['firstName'] as String?;
          final lastName = response['lastName'] as String?;
          final middleName = response['middleName'] as String?;
          // final refreshToken = response['refreshToken'] as String?; // If available

          // Store the access token
          if (token != null && token.isNotEmpty) {
            await storage.write(TOKEN, token);
            print("✅ Token stored successfully");
          } else {
            print("⚠️ Warning: No token received from server");
          }
          if (phone != null && phone.isNotEmpty) {
            await storage.write(PHONE, phone);
            print("✅ Phone stored successfully");
          }
          if (firstName != null && firstName.isNotEmpty) {
            await storage.write(FIRST_NAME, firstName);
            print("✅ First name stored successfully");
          }
          if (lastName != null && lastName.isNotEmpty) {
            await storage.write(LAST_NAME, lastName);
            print("✅ Last name stored successfully");
          }
          if (middleName != null && middleName.isNotEmpty) {
            await storage.write(MIDDLE_NAME, middleName);
            print("✅ Middle name stored successfully");
          }

          // Store refresh token if available (for future use)
          // if (refreshToken != null && refreshToken.isNotEmpty) {
          //   await storage.write(REFRESH_TOKEN, refreshToken);
          //   print("✅ Refresh token stored successfully");
          // }

          // Store user ID if available
          if (userId != null && userId.isNotEmpty) {
            await storage.write(USER_ID, userId);
            print("✅ User ID stored successfully");
          }

          // Check if emitter is still active before emitting
          if (!emit.isDone) {
            emit(LoginState(
              status: LoginStatus.success,
              message: response['message'] as String?,
            ));
          }
        } catch (storageError) {
          print("❌ Error storing login data: $storageError");
          if (!emit.isDone) {
            emit(LoginState(
              status: LoginStatus.failure,
              message: "Failed to save login information",
            ));
          }
        }
      } else {
        if (!emit.isDone) {
          emit(LoginState(
              status: LoginStatus.failure,
              message: response['message'] as String? ??
                  LocaleKeys.auth_login_login_failed.tr()));
        }
      }
    } catch (e) {
      print("❌ Login error: $e");
      if (!emit.isDone) {
        // Handle DioException and other errors
        String errorMessage;
        if (e is DioException) {
          errorMessage = SimpleErrorHandler.handleError(e);
        } else {
          errorMessage = e.toString();
        }

        emit(LoginState(
            status: LoginStatus.failure,
            message: errorMessage
        ));
      }
    }
  }
}