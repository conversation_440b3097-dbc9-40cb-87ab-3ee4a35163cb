part of 'login_bloc.dart';

sealed class LoginEvent extends Equatable {
  const LoginEvent();

  @override
  List<Object?> get props => [];
}
class LoginPhoneEvent extends LoginEvent {
  final String phone;
  final String appSignature;

  const LoginPhoneEvent(
      {required this.phone,
      required this.appSignature});

  @override
  List<Object> get props => [phone, appSignature];
}

class LoginWithPasswordEvent extends LoginEvent {
  final String phone;
  final String password;

  const LoginWithPasswordEvent(
      {required this.phone,
        required this.password});

  @override
  List<Object> get props => [phone, password];
}
