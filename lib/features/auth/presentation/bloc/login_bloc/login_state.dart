part of 'login_bloc.dart';

enum LoginStatus { initial, loading, success, failure, loadAgain }

class LoginState extends Equatable {
  final LoginStatus status;
  final String? message;

  const LoginState({
    this.status = LoginStatus.initial,
    this.message = '',
  });

  //copyWith method
  LoginState copyWith({
    LoginStatus? status,
    String? message,
  }) {
    return LoginState(
      status: status ?? this.status,
      message: message ?? this.message,
    );
  }

  @override
  List<Object?> get props => [status, message];
}
