part of 'sms_verification_bloc.dart';

abstract class SmsVerificationEvent extends Equatable {
  const SmsVerificationEvent();

  @override
  List<Object> get props => [];
}

class VerifySmsCodeEvent extends SmsVerificationEvent {
  final String phone;
  final String verifyCode;

  const VerifySmsCodeEvent({
    required this.phone,
    required this.verifyCode,
  });

  @override
  List<Object> get props => [phone, verifyCode];
}
