part of 'sms_verification_bloc.dart';

enum SmsVerificationStatus { initial, loading, success, failure }

class SmsVerificationState extends Equatable {
  final SmsVerificationStatus status;
  final String? message;
  final String? token;
  final String? userId;

  const SmsVerificationState({
    this.status = SmsVerificationStatus.initial,
    this.message,
    this.token,
    this.userId,
  });

  SmsVerificationState copyWith({
    SmsVerificationStatus? status,
    String? message,
    String? token,
    String? userId,
  }) {
    return SmsVerificationState(
      status: status ?? this.status,
      message: message ?? this.message,
      token: token ?? this.token,
      userId: userId ?? this.userId,
    );
  }

  @override
  List<Object?> get props => [status, message, token, userId];
}
