import 'package:auto_sms_verification/auto_sms_verification.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:mobil_kochat/core/utils/target.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../di/dependency_injection.dart';
import '../../../../navigation_page.dart';
import '../bloc/login_bloc/login_bloc.dart';
import 'sms_verification_page.dart';
import '../../../../translations/locale_keys.g.dart';

class LoginPage extends StatelessWidget {
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => di<LoginBloc>(),
      child: const _LoginPageContent(),
    );
  }
}

class _LoginPageContent extends StatefulWidget {
  const _LoginPageContent();

  @override
  State<_LoginPageContent> createState() => _LoginPageContentState();
}

class _LoginPageContentState extends State<_LoginPageContent> {
  final _phoneController = TextEditingController(text: '+998 ');
  final _passwordController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _agreeToTerms = false;
  bool _isLoading = false;
  bool _isPasswordVisible = false;
  String? appSignature;

  @override
  void dispose() {
    _phoneController.dispose();
    super.dispose();
  }

  void _formatPhoneNumber(String value) {
    // Don't format if user is trying to delete the prefix
    if (value.length < 5) {
      _phoneController.value = const TextEditingValue(
        text: '+998 ',
        selection: TextSelection.collapsed(offset: 5),
      );
      return;
    }

    // Get current cursor position
    int cursorPosition = _phoneController.selection.baseOffset;

    // Extract digits only (excluding country code)
    String allDigits = value.replaceAll(RegExp(r'[^\d]'), '');
    if (allDigits.startsWith('998')) {
      allDigits = allDigits.substring(3);
    }

    // Limit to 9 digits
    if (allDigits.length > 9) {
      allDigits = allDigits.substring(0, 9);
    }

    // Format: +998 XX XXX XX XX
    String formatted = '+998 ';
    if (allDigits.isNotEmpty) {
      // Add first 2 digits
      if (allDigits.length >= 1) {
        formatted += allDigits.substring(0, allDigits.length >= 2 ? 2 : 1);
        if (allDigits.length > 2) {
          formatted += ' ';
          // Add next 3 digits
          formatted += allDigits.substring(
              2, allDigits.length >= 5 ? 5 : allDigits.length);
          if (allDigits.length > 5) {
            formatted += ' ';
            // Add next 2 digits
            formatted += allDigits.substring(
                5, allDigits.length >= 7 ? 7 : allDigits.length);
            if (allDigits.length > 7) {
              formatted += ' ';
              // Add last 2 digits
              formatted += allDigits.substring(7);
            }
          }
        }
      }
    }

    // Only update if the formatted text is different
    if (formatted != value) {
      // Calculate new cursor position
      int newCursorPosition = formatted.length;

      // If user was typing in the middle, try to maintain relative position
      if (cursorPosition < value.length) {
        // Count digits before cursor in original text
        int digitsBefore = 0;
        for (int i = 0; i < cursorPosition && i < value.length; i++) {
          if (RegExp(r'\d').hasMatch(value[i])) {
            digitsBefore++;
          }
        }

        // Find corresponding position in formatted text
        int digitCount = 0;
        for (int i = 0; i < formatted.length; i++) {
          if (RegExp(r'\d').hasMatch(formatted[i])) {
            digitCount++;
            if (digitCount == digitsBefore) {
              newCursorPosition = i + 1;
              break;
            }
          }
        }
      }

      // Ensure cursor is not before the prefix
      newCursorPosition = newCursorPosition.clamp(5, formatted.length);

      _phoneController.value = TextEditingValue(
        text: formatted,
        selection: TextSelection.collapsed(offset: newCursorPosition),
      );
    }
  }

  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate() || !_agreeToTerms) {
      if (!_agreeToTerms) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(LocaleKeys.auth_login_terms_error.tr(),
                style: const TextStyle(color: Colors.white)),
            backgroundColor: AppColors.cReddishColor,
            behavior: SnackBarBehavior.floating,
            margin: EdgeInsets.only(
              bottom: MediaQuery.of(context).size.height - 150,
              left: 16,
              right: 16,
            ),
          ),
        );
      }
      return;
    }

    await _performLogin();
  }

  Future<void> _performLogin() async {
    // Extract phone number without formatting
    String phoneNumber = _phoneController.text
        .trim()
        .replaceAll('+998 ', '')
        .replaceAll(' ', '');
    String appSignature = 'undefined';
    if (isAndroid()) {
      appSignature = await AutoSmsVerification.appSignature() ?? 'undefined';
      // CustomToast.showToast('Mac: ' + macAddress + 'signature' + appSignature.toString());
    }

    // Guard the BuildContext usage with mounted check
    if (!mounted) return;

    // Trigger login via BLoC
    // context.read<LoginBloc>().add(LoginPhoneEvent(
    //       phone: phoneNumber,
    //       appSignature: appSignature,
    //     ));

    context.read<LoginBloc>().add(LoginWithPasswordEvent(
        phone: phoneNumber, password: _passwordController.text));
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<LoginBloc, LoginState>(
      listener: (context, state) {
        if (state.status == LoginStatus.loading) {
          setState(() {
            _isLoading = true;
          });
        } else {
          setState(() {
            _isLoading = false;
          });
        }

        if (state.status == LoginStatus.success) {
          // Navigate to SMS verification page on successful login
          // Navigator.push(
          //   context,
          //   MaterialPageRoute(
          //     builder: (context) => SmsVerificationPage(
          //       phoneNumber: _phoneController.text,
          //     ),
          //   ),
          // );

          // Navigate to HomePage successful login
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => NavigationPage(),
            ),
          );
        } else if (state.status == LoginStatus.failure) {
          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  state.message ?? LocaleKeys.auth_login_login_error.tr(),
                  style: const TextStyle(color: Colors.white)),
              backgroundColor: Theme.of(context).colorScheme.error,
              behavior: SnackBarBehavior.floating,
              margin: EdgeInsets.only(
                bottom: MediaQuery.of(context).size.height - 150,
                left: 16,
                right: 16,
              ),
            ),
          );
        }
      },
      builder: (context, state) {
        return Scaffold(
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          resizeToAvoidBottomInset: false,
          body: SafeArea(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Gap(60.h),

                    // Title
                    Text(
                      LocaleKeys.auth_login_title.tr(),
                      style: AppTextStyles.headlineMedium.copyWith(
                        color: Theme.of(context).colorScheme.onSurface,
                        fontWeight: FontWeight.w600,
                      ),
                    ),

                    Gap(8.h),

                    // Subtitle
                    Text(
                      LocaleKeys.auth_login_subtitle.tr(),
                      style: AppTextStyles.bodyLarge.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                        height: 1.4,
                      ),
                    ),

                    Gap(32.h),

                    // Phone number label
                    Text(
                      LocaleKeys.auth_login_phone_label.tr(),
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.cTextGrayColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),

                    Gap(12.h),

                    // Phone number input
                    TextFormField(
                      controller: _phoneController,
                      keyboardType: TextInputType.phone,
                      style: AppTextStyles.bodyLarge.copyWith(
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                      decoration: InputDecoration(
                        filled: true,
                        fillColor: Theme.of(context).colorScheme.surface,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.r),
                          borderSide: BorderSide.none,
                        ),
                        contentPadding: EdgeInsets.symmetric(
                          vertical: 16.h,
                          horizontal: 16.w,
                        ),
                        hintText: LocaleKeys.auth_login_phone_placeholder.tr(),
                        hintStyle: AppTextStyles.bodyLarge.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                      onChanged: _formatPhoneNumber,
                      validator: (value) {
                        if (value == null || value.length < 17) {
                          return LocaleKeys.auth_login_phone_validation.tr();
                        }
                        return null;
                      },
                    ),

                    // Password input
                    Gap(12.h),
                    // Password input
                    TextFormField(
                      controller: _passwordController,
                      obscureText: !_isPasswordVisible,
                      style: AppTextStyles.bodyLarge.copyWith(
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                      decoration: InputDecoration(
                        //Eye
                        suffixIcon: IconButton(
                          icon: Icon(
                            _isPasswordVisible
                                ? Icons.visibility_off
                                : Icons.visibility,
                            color:
                                Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                          onPressed: () {
                            setState(() {
                              _isPasswordVisible = !_isPasswordVisible;
                            });
                          },
                        ),
                        filled: true,
                        fillColor: Theme.of(context).colorScheme.surface,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.r),
                          borderSide: BorderSide.none,
                        ),
                        contentPadding: EdgeInsets.symmetric(
                          vertical: 16.h,
                          horizontal: 16.w,
                        ),
                        hintText: "Parol",
                        hintStyle: AppTextStyles.bodyLarge.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ),

                    Gap(24.h),
                    // Terms checkbox
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20.w,
                          height: 20.h,
                          child: Checkbox(
                            value: _agreeToTerms,
                            onChanged: (value) {
                              setState(() {
                                _agreeToTerms = value ?? false;
                              });
                            },
                            activeColor: AppColors.cFirstColor,
                            checkColor: AppColors.white,
                            side: BorderSide(
                              color: _agreeToTerms
                                  ? AppColors.cFirstColor
                                  : AppColors.cTextGrayColor,
                              width: 2,
                            ),
                          ),
                        ),
                        Gap(12.w),
                        Expanded(
                          child: InkWell(
                            onTap: () {
                              setState(() {
                                _agreeToTerms = !_agreeToTerms;
                              });
                            },
                            child: Text(
                              LocaleKeys.auth_login_agree_terms.tr(),
                              style: AppTextStyles.bodyMedium.copyWith(
                                color: AppColors.cTextGrayColor,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),

                    Gap(40.h),

                    // Send button
                    SizedBox(
                      width: double.infinity,
                      height: 52.h,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _handleLogin,
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              Theme.of(context).colorScheme.primary,
                          foregroundColor:
                              Theme.of(context).colorScheme.onPrimary,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          elevation: 0,
                        ),
                        child: _isLoading
                            ? SizedBox(
                                width: 20.h,
                                height: 20.h,
                                child: CircularProgressIndicator(
                                  color:
                                      Theme.of(context).colorScheme.onPrimary,
                                  strokeWidth: 2,
                                ),
                              )
                            : Text(
                                LocaleKeys.auth_login_login_button.tr(),
                                style: AppTextStyles.buttonText,
                              ),
                      ),
                    ),

                    const Spacer(),

                    // Demo login button
                    // Center(
                    //   child: TextButton(
                    //     onPressed: _isLoading ? null : _demoLogin,
                    //     child: Text(
                    //       LocaleKeys.auth_login_demo_login.tr(),
                    //       style: AppTextStyles.bodyLarge.copyWith(
                    //         color: Theme.of(context).colorScheme.primary,
                    //         fontWeight: FontWeight.w600,
                    //       ),
                    //     ),
                    //   ),
                    // ),

                    Gap(40.h),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
