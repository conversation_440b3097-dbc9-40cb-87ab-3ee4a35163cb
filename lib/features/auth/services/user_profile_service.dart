import 'package:dio/dio.dart';
import 'package:get_storage/get_storage.dart';
import '../../../core/network/network_info.dart';
import '../../../core/utils/api_path.dart';
import '../../../core/utils/app_constants.dart';
import '../../../core/utils/jwt_decoder.dart';
import '../models/user_profile_model.dart';

class UserProfileService {
  final Dio _dio;
  final GetStorage _storage;
  final NetworkInfo _networkInfo;

  static const String _cacheKey = 'cached_user_profile';
  static const String _cacheTimeKey = 'profile_cache_time';
  static const Duration _cacheValidDuration = Duration(hours: 24);

  UserProfileService({
    required Dio dio,
    required GetStorage storage,
    required NetworkInfo networkInfo,
  })  : _dio = dio,
        _storage = storage,
        _networkInfo = networkInfo;

  /// Get user profile with caching
  Future<UserProfile?> getUserProfile({bool forceRefresh = false}) async {
    try {
      // Check if we have a valid cached profile first
      if (!forceRefresh) {
        final cachedProfile = _getCachedProfile();
        if (cachedProfile != null) {
          return cachedProfile;
        }
      }

      // Try to get profile from API
      final apiProfile = await _fetchProfileFromApi();
      if (apiProfile != null) {
        await _cacheProfile(apiProfile);
        return apiProfile;
      }

      // Fallback to cached profile even if expired
      return _getCachedProfile(ignoreExpiry: true);
    } catch (e) {
      print('Error getting user profile: $e');
      // Return cached profile as fallback
      return _getCachedProfile(ignoreExpiry: true);
    }
  }

  /// Get user profile from JWT token (limited data)
  UserProfile? getUserProfileFromToken() {
    try {
      final token = _storage.read(TOKEN);
      if (token == null) return null;

      final userId = JwtDecoder.getUserId(token);
      final firstName = JwtDecoder.getFirstName(token);
      final lastName = JwtDecoder.getLastName(token);
      final middleName = JwtDecoder.getMiddleName(token);
      final phone = JwtDecoder.getPhone(token);
      final role = JwtDecoder.getRole(token);

      return UserProfile(
        id: userId,
        firstName: firstName,
        lastName: lastName,
        middleName: middleName,
        phone: phone,
        role: role,
        active: true, // Assume active if token is valid
      );
    } catch (e) {
      print('Error extracting profile from token: $e');
      return null;
    }
  }

  /// Fetch profile from API
  Future<UserProfile?> _fetchProfileFromApi() async {
    try {
      if (!await _networkInfo.isConnected) {
        return null;
      }

      final userId = _storage.read(USER_ID);
      if (userId == null) return null;

      final response = await _dio.get('${ApiPath.profilePath}/$userId');
      
      if (response.statusCode == 200 && response.data != null) {
        return UserProfile.fromJson(response.data);
      }
    } catch (e) {
      print('Error fetching profile from API: $e');
    }
    return null;
  }

  /// Get cached profile
  UserProfile? _getCachedProfile({bool ignoreExpiry = false}) {
    try {
      final cachedData = _storage.read(_cacheKey);
      final cacheTime = _storage.read(_cacheTimeKey);

      if (cachedData == null) return null;

      // Check cache expiry
      if (!ignoreExpiry && cacheTime != null) {
        final cacheDateTime = DateTime.parse(cacheTime);
        final now = DateTime.now();
        if (now.difference(cacheDateTime) > _cacheValidDuration) {
          return null; // Cache expired
        }
      }

      return UserProfile.fromJson(Map<String, dynamic>.from(cachedData));
    } catch (e) {
      print('Error reading cached profile: $e');
      return null;
    }
  }

  /// Cache profile data
  Future<void> _cacheProfile(UserProfile profile) async {
    try {
      await _storage.write(_cacheKey, profile.toJson());
      await _storage.write(_cacheTimeKey, DateTime.now().toIso8601String());
    } catch (e) {
      print('Error caching profile: $e');
    }
  }

  /// Clear cached profile
  Future<void> clearCache() async {
    try {
      await _storage.remove(_cacheKey);
      await _storage.remove(_cacheTimeKey);
    } catch (e) {
      print('Error clearing profile cache: $e');
    }
  }

  /// Get current user's display info for app bar
  Future<Map<String, String>> getUserDisplayInfo() async {
    try {
      // First try to get from full profile
      final profile = await getUserProfile();
      if (profile != null && !profile.isEmpty) {
        return {
          'name': profile.displayName,
          'phone': profile.formattedPhone,
        };
      }

      // Fallback to token data
      final tokenProfile = getUserProfileFromToken();
      if (tokenProfile != null) {
        return {
          'name': tokenProfile.displayName,
          'phone': tokenProfile.formattedPhone,
        };
      }

      // Final fallback
      return {
        'name': 'Foydalanuvchi',
        'phone': '',
      };
    } catch (e) {
      print('Error getting user display info: $e');
      return {
        'name': 'Foydalanuvchi',
        'phone': '',
      };
    }
  }

  /// Check if user is authenticated
  bool isAuthenticated() {
    final token = _storage.read(TOKEN);
    if (token == null) return false;
    return !JwtDecoder.isTokenExpired(token);
  }

  /// Get current user ID
  String? getCurrentUserId() {
    final token = _storage.read(TOKEN);
    if (token == null) return null;
    return JwtDecoder.getUserId(token);
  }
}
