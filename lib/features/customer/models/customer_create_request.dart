class CustomerCreateRequest {
  final String fullName;
  final String birthDay;
  final String passport;
  final String phone;
  final String jshshir;
  final String address;
  final String province;
  final String region;

  CustomerCreateRequest({
    required this.fullName,
    required this.birthDay,
    required this.passport,
    required this.phone,
    required this.jshshir,
    required this.address,
    required this.province,
    required this.region,
  });

  Map<String, dynamic> toJson() {
    return {
      'fullName': fullName,
      'birthDay': birthDay,
      'passport': passport,
      'phone': phone,
      'jshshir': jshshir,
      'address': address,
      'province': province,
      'region': region,
    };
  }
}