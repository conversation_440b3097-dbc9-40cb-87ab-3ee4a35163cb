import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:get_storage/get_storage.dart';
import '../../../core/network/network_info.dart';
import '../models/customer_model.dart';
import '../models/customer_create_request.dart';

class CustomerService {
  final Dio dio;
  final GetStorage storage;
  final NetworkInfo networkInfo;

  CustomerService({
    required this.dio,
    required this.storage,
    required this.networkInfo,
  });

  Future<PaginatedResponse<Customer>?> getCustomers({
    required int page,
    required int limit,
    String? search,
    bool forceRefresh = false,
  }) async {
    if (!await networkInfo.isConnected) {
      throw Exception('No internet connection');
    }

    try {
      String baseUrl = '/mobile/client/getAll';
      Map<String, dynamic> params = {
        'page': page,
        'limit': limit,
      };
      if (search != null && search.isNotEmpty) {
        params['search'] = search;
      }

      final String? token = storage.read('token');
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await dio.get(
        baseUrl,
        queryParameters: params,
        options: Options(
          headers: {'Authorization': 'Bearer $token'},
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data as Map<String, dynamic>;
        final paginated = PaginatedResponse.fromJson(data, Customer.fromJson);
        return paginated;
      } else {
        throw Exception('Failed to load customers: ${response.statusMessage}');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw Exception('Authentication failed');
      }
      rethrow;
    } catch (e) {
      throw Exception('Error loading customers: $e');
    }
  }

  Future<List<Customer>> searchCustomers(String query) async {
    final paginated = await getCustomers(
      page: 1,
      limit: 100,
      search: query,
    );
    return paginated?.docs ?? [];
  }

  Future<List<Province>> getProvinces() async {
    if (!await networkInfo.isConnected) {
      throw Exception('No internet connection');
    }

    try {
      final String? token = storage.read('token');
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await dio.get(
        '/mobile/province/getAll',
        queryParameters: {'page': 1, 'limit': 100},
        options: Options(
          headers: {'Authorization': 'Bearer $token'},
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data as Map<String, dynamic>;
        final provinces =
        (data['docs'] as List).map((e) => Province.fromJson(e)).toList();
        return provinces;
      } else {
        throw Exception('Failed to load provinces: ${response.statusMessage}');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw Exception('Authentication failed');
      }
      rethrow;
    } catch (e) {
      throw Exception('Error loading provinces: $e');
    }
  }

  Future<List<Region>> getRegions({required String provinceId}) async {
    if (!await networkInfo.isConnected) {
      throw Exception('No internet connection');
    }

    try {
      final String? token = storage.read('token');
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await dio.get(
        '/mobile/region/getAll',
        queryParameters: {'province': provinceId, 'page': 1, 'limit': 100},
        options: Options(
          headers: {'Authorization': 'Bearer $token'},
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data as Map<String, dynamic>;
        final regions =
        (data['docs'] as List).map((e) => Region.fromJson(e)).toList();
        return regions;
      } else {
        throw Exception('Failed to load regions: ${response.statusMessage}');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw Exception('Authentication failed');
      }
      rethrow;
    } catch (e) {
      throw Exception('Error loading regions: $e');
    }
  }

  Future<bool> createCustomer(CustomerCreateRequest request) async {
    if (!await networkInfo.isConnected) {
      throw Exception('No internet connection');
    }

    try {
      final String? token = storage.read('token');
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await dio.post(
        '/mobile/client',
        data: request.toJson(),
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
        ),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return true;
      } else {
        throw Exception('Failed to create customer: ${response.statusMessage}');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw Exception('Authentication failed');
      }
      throw Exception('Error creating customer: ${e.message}');
    } catch (e) {
      throw Exception('Error creating customer: $e');
    }
  }

  Future<bool> updateCustomer(String id, CustomerCreateRequest request) async {
    if (!await networkInfo.isConnected) {
      throw Exception('No internet connection');
    }

    try {
      final String? token = storage.read('token');
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await dio.put(
        '/mobile/client/$id',
        data: request.toJson(),
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
        ),
      );

      if (response.statusCode == 200) {
        return true;
      } else {
        throw Exception('Failed to update customer: ${response.statusMessage}');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw Exception('Authentication failed');
      }
      throw Exception('Error updating customer: ${e.message}');
    } catch (e) {
      throw Exception('Error updating customer: $e');
    }
  }
}