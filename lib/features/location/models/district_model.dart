import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import '../../customer/models/customer_model.dart';

@JsonSerializable()
class District extends Equatable {
  @Json<PERSON>ey(name: '_id')
  final String id;
  
  final String title;
  final String? desc;
  final Province? province;
  final Region? region;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const District({
    required this.id,
    required this.title,
    this.desc,
    this.province,
    this.region,
    this.createdAt,
    this.updatedAt,
  });

  factory District.fromJson(Map<String, dynamic> json) {
    return District(
      id: json['_id'] ?? '',
      title: json['title'] ?? '',
      desc: json['desc'] ?? '',
      region: json['region'] != null ? Region.fromJson(json['region']) : null,
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'title': title,
      'desc': desc,
      'region': region?.toJson(),
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  District copyWith({
    String? id,
    String? title,
    String? desc,
    Province? province,
    Region? region,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return District(
      id: id ?? this.id,
      title: title ?? this.title,
      desc: desc ?? this.desc,
      province: province ?? this.province,
      region: region ?? this.region,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [id, title, desc, province, region, createdAt, updatedAt];

  @override
  String toString() => 'District(id: $id, title: $title, desc: $desc, province: ${province?.title}, region: ${region?.title})';
}

@JsonSerializable()
class DistrictResponse extends Equatable {
  final List<District> docs;
  final int totalDocs;
  final int limit;
  final int totalPages;
  final int page;
  final int pagingCounter;
  final bool hasPrevPage;
  final bool hasNextPage;
  final int? prevPage;
  final int? nextPage;

  const DistrictResponse({
    required this.docs,
    required this.totalDocs,
    required this.limit,
    required this.totalPages,
    required this.page,
    required this.pagingCounter,
    required this.hasPrevPage,
    required this.hasNextPage,
    this.prevPage,
    this.nextPage,
  });

  factory DistrictResponse.fromJson(Map<String, dynamic> json) {
    return DistrictResponse(
      docs: (json['docs'] as List<dynamic>? ?? [])
          .map((district) => District.fromJson(district))
          .toList(),
      totalDocs: json['totalDocs'] ?? 0,
      limit: json['limit'] ?? 10,
      totalPages: json['totalPages'] ?? 1,
      page: json['page'] ?? 1,
      pagingCounter: json['pagingCounter'] ?? 1,
      hasPrevPage: json['hasPrevPage'] ?? false,
      hasNextPage: json['hasNextPage'] ?? false,
      prevPage: json['prevPage'],
      nextPage: json['nextPage'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'docs': docs.map((district) => district.toJson()).toList(),
      'totalDocs': totalDocs,
      'limit': limit,
      'totalPages': totalPages,
      'page': page,
      'pagingCounter': pagingCounter,
      'hasPrevPage': hasPrevPage,
      'hasNextPage': hasNextPage,
      'prevPage': prevPage,
      'nextPage': nextPage,
    };
  }

  @override
  List<Object?> get props => [
        docs,
        totalDocs,
        limit,
        totalPages,
        page,
        pagingCounter,
        hasPrevPage,
        hasNextPage,
        prevPage,
        nextPage,
      ];
}
