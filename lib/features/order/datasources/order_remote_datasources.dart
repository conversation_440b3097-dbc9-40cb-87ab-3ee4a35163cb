import 'dart:io';

import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:http_parser/http_parser.dart';
import 'package:path_provider/path_provider.dart';
import '../../../core/function/functions.dart';
import '../../../core/network/network_info.dart';
import '../../../core/utils/api_path.dart';
import '../../../translations/locale_keys.g.dart';
import '../models/order_model.dart';

class OrderRemoteDatasourceImpl {
  final NetworkInfo networkInfo;
  final Dio dio;

  OrderRemoteDatasourceImpl({
    required this.networkInfo,
    required this.dio,
  });

  /// Get all orders with pagination and filters
  Future<OrdersResponse> getAllOrders({
    int page = 1,
    int limit = 10,
    String? status,
    String? date,
    String? search,
  }) async {
    // Check network connectivity first
    if (!await networkInfo.isConnected) {
      throw DioException(
        requestOptions: RequestOptions(
          path: ApiPath.ordersGetAllPath,
        ),
        type: DioExceptionType.connectionError,
        message: LocaleKeys.common_no_internet_connection.tr(),
      );
    }

    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (status != null) queryParams['status'] = status;
      if (date != null) queryParams['date'] = date;
      if (search != null && search.isNotEmpty) queryParams['search'] = search;

      final response = await dio.get(
        ApiPath.ordersGetAllPath,
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final data = response.data;

        // API returns array directly instead of paginated response
        if (data is List) {
          final orders = <Order>[];
          for (var json in data) {
            try {
              orders.add(Order.fromJson(json));
            } catch (e) {
              print('Error parsing order: $e');
              print('Order JSON: $json');
              // Skip invalid orders instead of failing completely
            }
          }

          // Apply client-side search filtering if search query is provided
          List<Order> filteredOrders = orders;
          if (search != null && search.isNotEmpty) {
            filteredOrders = orders.where((order) {
              final searchLower = search.toLowerCase();

              // Search in client name
              final clientName = order.client?.fullName?.toLowerCase() ?? '';
              if (clientName.contains(searchLower)) return true;

              // Search in client phone
              final clientPhone = order.client?.phone?.toLowerCase() ?? '';
              if (clientPhone.contains(searchLower)) return true;

              // Search in order ID
              final orderId = order.id.toLowerCase();
              if (orderId.contains(searchLower)) return true;

              // Search in worker name
              final workerName = order.worker.toLowerCase();
              if (workerName.contains(searchLower)) return true;

              return false;
            }).toList();
          }

          return OrdersResponse(
            orders: filteredOrders,
            totalDocs: filteredOrders.length,
            limit: limit,
            totalPages: 1,
            page: page,
            hasNextPage: false,
            hasPrevPage: false,
          );
        } else {
          // Standard paginated response
          try {
            return OrdersResponse.fromJson(data);
          } catch (e) {
            print('Error parsing OrdersResponse: $e');
            print('Response data: $data');
            rethrow;
          }
        }
      } else {
        throw DioException(
          requestOptions: RequestOptions(path: ApiPath.ordersGetAllPath),
          response: response,
          type: DioExceptionType.badResponse,
          message: 'Server xatoligi',
        );
      }
    } on DioException catch (e) {
      print('DioException in getAllOrders: $e');
      rethrow;
    } catch (e) {
      print('Exception in getAllOrders: $e');
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.ordersGetAllPath),
        type: DioExceptionType.unknown,
        error: e,
      );
    }
  }

  /// Get single order by ID
  Future<Order> getOrderById(String orderId) async {
    // Check network connectivity first
    if (!await networkInfo.isConnected) {
      throw DioException(
        requestOptions: RequestOptions(
          path: '${ApiPath.ordersGetOnePath}/$orderId',
        ),
        type: DioExceptionType.connectionError,
        message: LocaleKeys.common_no_internet_connection.tr(),
      );
    }

    try {
      final response = await dio.get('${ApiPath.ordersGetOnePath}/$orderId');

      if (response.statusCode == 200) {
        return Order.fromJson(response.data);
      } else {
        throw DioException(
          requestOptions: RequestOptions(path: '${ApiPath.ordersGetOnePath}/$orderId'),
          response: response,
          type: DioExceptionType.badResponse,
          message: 'Buyurtma topilmadi',
        );
      }
    } on DioException catch (e) {
      print('DioException in getOrderById: $e');
      rethrow;
    } catch (e) {
      print('Exception in getOrderById: $e');
      throw DioException(
        requestOptions: RequestOptions(path: '${ApiPath.ordersGetOnePath}/$orderId'),
        type: DioExceptionType.unknown,
        error: e,
      );
    }
  }

  /// Create new order with multipart form data
  Future<Map<String, dynamic>> createOrder(CreateOrderRequest request) async {
    // Check network connectivity first
    if (!await networkInfo.isConnected) {
      throw DioException(
        requestOptions: RequestOptions(
          path: ApiPath.ordersCreatePath,
        ),
        type: DioExceptionType.connectionError,
        message: LocaleKeys.common_no_internet_connection.tr(),
      );
    }

    try {
      final formData = FormData();

      // Add basic fields
      formData.fields.addAll([
        MapEntry('client', request.client),
        MapEntry('totalPrice', request.totalPrice.toString()),
        MapEntry('paymentType', request.paymentType.toString()),
        MapEntry('province', request.province),
        MapEntry('region', request.region),
        MapEntry('desc', '-'),
        MapEntry('district', request.district),
        MapEntry('location[longitude]', request.location.longitude),
        MapEntry('location[latitude]', request.location.latitude),
      ]);

      // Add products array
      for (int i = 0; i < request.products.length; i++) {
        final product = request.products[i];
        formData.fields.addAll([
          MapEntry('products[$i][tree]', product.treeId),
          MapEntry('products[$i][variety]', product.variety?.id ?? ''),
          MapEntry('products[$i][count]', product.count.toString()),
          MapEntry('products[$i][price]', product.price.toString()),
          MapEntry('products[$i][totalPrice]', product.totalPrice.toString()),
        ]);
      }

      // Add photos
      for (final photo in request.photos) {
        formData.files.add(MapEntry(
          'files',
          await MultipartFile.fromFile(
            photo.path,
            contentType: getMediaType(photo.path),
            filename: 'order_photo_${DateTime.now().millisecondsSinceEpoch}.jpg',
          ),
        ));
      }

      //Save UintList8 file signature to file
      // if (request.signature != null) {
      //   final tempDir = await getTemporaryDirectory();
      //   final file = File('${tempDir.path}/signature_${DateTime.now().millisecondsSinceEpoch}.png');
      //   print('Signature file path: ${file.path}');
      //   await file.writeAsBytes(request.signature!);
      // }

      // Add contract/signature PDF/PNG file
      if (request.contractFile != null) {
        formData.files.add(MapEntry(
          'file',
          await MultipartFile.fromBytes(
            contentType: MediaType('image', 'png'),
            request.signature!.buffer.asUint8List(),
            filename: 'signature_${DateTime.now().millisecondsSinceEpoch}.png',
          ),
        ));
      }


      Map<dynamic, dynamic> formDataMap = formData.fields.asMap();
      print('Form data: $formDataMap');

      final response = await dio.post(
        ApiPath.ordersCreatePath,
        data: formData,
        options: Options(
          contentType: 'multipart/form-data',
          sendTimeout: const Duration(minutes: 5), // Increase timeout for file uploads
          receiveTimeout: const Duration(minutes: 5),
        ),
      );

      if (response.statusCode == 201) {
        final data = response.data;
        if (data.containsKey('message')) {
          return {
            'success': true,
            'message': data['message'] ?? 'Buyurtma muvaffaqiyatli yaratildi',
          };
        }
        return {
          'success': true,
          'message': 'Buyurtma muvaffaqiyatli yaratildi',
        };
      } else {
        return {
          'success': false,
          'message': 'Server xatoligi',
        };
      }
    } on DioException catch (e) {
      print('DioException in createOrder: $e');

      // Handle specific error cases
      if (e.type == DioExceptionType.connectionTimeout ||
          e.type == DioExceptionType.sendTimeout ||
          e.type == DioExceptionType.receiveTimeout) {
        throw DioException(
          requestOptions: e.requestOptions,
          type: e.type,
          message: 'Vaqt tugadi. Iltimos qaytadan urinib ko\'ring.',
        );
      }

      if (e.response?.statusCode == 413) {
        throw DioException(
          requestOptions: e.requestOptions,
          type: DioExceptionType.badResponse,
          message: 'Fayl hajmi juda katta. Kichikroq fayl tanlang.',
        );
      }

      rethrow;
    } catch (e) {
      print('Exception in createOrder: $e');
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.ordersCreatePath),
        type: DioExceptionType.unknown,
        error: e,
      );
    }
  }

  /// Get all clients for order creation
  Future<ClientsResponse> getAllClients({
    int page = 1,
    int limit = 10,
    String? province,
    String? region,
    String? search,
  }) async {
    // Check network connectivity first
    if (!await networkInfo.isConnected) {
      throw DioException(
        requestOptions: RequestOptions(
          path: ApiPath.clientsGetAllPath,
        ),
        type: DioExceptionType.connectionError,
        message: LocaleKeys.common_no_internet_connection.tr(),
      );
    }

    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (province != null) queryParams['province'] = province;
      if (region != null) queryParams['region'] = region;
      if (search != null) queryParams['search'] = search;

      final response = await dio.get(
        ApiPath.clientsGetAllPath,
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        return ClientsResponse.fromJson(response.data);
      } else {
        throw DioException(
          requestOptions: RequestOptions(path: ApiPath.clientsGetAllPath),
          response: response,
          type: DioExceptionType.badResponse,
          message: 'Server xatoligi',
        );
      }
    } on DioException catch (e) {
      print('DioException in getAllClients: $e');
      rethrow;
    } catch (e) {
      print('Exception in getAllClients: $e');
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.clientsGetAllPath),
        type: DioExceptionType.unknown,
        error: e,
      );
    }
  }

  /// Get all trees for order creation
  Future<TreesResponse> getAllTrees({
    int page = 1,
    int limit = 10,
    String? variety,
  }) async {
    // Check network connectivity first
    if (!await networkInfo.isConnected) {
      throw DioException(
        requestOptions: RequestOptions(
          path: ApiPath.treesGetAllPath,
        ),
        type: DioExceptionType.connectionError,
        message: LocaleKeys.common_no_internet_connection.tr(),
      );
    }

    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (variety != null) queryParams['variety'] = variety;

      final response = await dio.get(
        ApiPath.treesGetAllPath,
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        return TreesResponse.fromJson(response.data);
      } else {
        throw DioException(
          requestOptions: RequestOptions(path: ApiPath.treesGetAllPath),
          response: response,
          type: DioExceptionType.badResponse,
          message: 'Server xatoligi',
        );
      }
    } on DioException catch (e) {
      print('DioException in getAllTrees: $e');
      rethrow;
    } catch (e) {
      print('Exception in getAllTrees: $e');
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.treesGetAllPath),
        type: DioExceptionType.unknown,
        error: e,
      );
    }
  }

  /// Get all provinces for order creation
  Future<ProvincesResponse> getAllProvinces({
    int page = 1,
    int limit = 10,
  }) async {
    // Check network connectivity first
    if (!await networkInfo.isConnected) {
      throw DioException(
        requestOptions: RequestOptions(
          path: ApiPath.provincesGetAllPath,
        ),
        type: DioExceptionType.connectionError,
        message: LocaleKeys.common_no_internet_connection.tr(),
      );
    }

    try {
      final response = await dio.get(
        ApiPath.provincesGetAllPath,
        queryParameters: {
          'page': page,
          'limit': limit,
        },
      );

      if (response.statusCode == 200) {
        return ProvincesResponse.fromJson(response.data);
      } else {
        throw DioException(
          requestOptions: RequestOptions(path: ApiPath.provincesGetAllPath),
          response: response,
          type: DioExceptionType.badResponse,
          message: 'Server xatoligi',
        );
      }
    } on DioException catch (e) {
      print('DioException in getAllProvinces: $e');
      rethrow;
    } catch (e) {
      print('Exception in getAllProvinces: $e');
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.provincesGetAllPath),
        type: DioExceptionType.unknown,
        error: e,
      );
    }
  }

  /// Get regions by province
  Future<RegionsResponse> getRegionsByProvince({
    required String provinceId,
    int page = 1,
    int limit = 10,
  }) async {
    // Check network connectivity first
    if (!await networkInfo.isConnected) {
      throw DioException(
        requestOptions: RequestOptions(
          path: ApiPath.regionsGetAllPath,
        ),
        type: DioExceptionType.connectionError,
        message: LocaleKeys.common_no_internet_connection.tr(),
      );
    }

    try {
      final response = await dio.get(
        ApiPath.regionsGetAllPath,
        queryParameters: {
          'page': page,
          'limit': limit,
          'province': provinceId,
        },
      );

      if (response.statusCode == 200) {
        return RegionsResponse.fromJson(response.data);
      } else {
        throw DioException(
          requestOptions: RequestOptions(path: ApiPath.regionsGetAllPath),
          response: response,
          type: DioExceptionType.badResponse,
          message: 'Server xatoligi',
        );
      }
    } on DioException catch (e) {
      print('DioException in getRegionsByProvince: $e');
      rethrow;
    } catch (e) {
      print('Exception in getRegionsByProvince: $e');
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.regionsGetAllPath),
        type: DioExceptionType.unknown,
        error: e,
      );
    }
  }

  /// Get districts by region
  Future<DistrictsResponse> getDistrictsByRegion({
    required String regionId,
    int page = 1,
    int limit = 10,
  }) async {
    // Check network connectivity first
    if (!await networkInfo.isConnected) {
      throw DioException(
        requestOptions: RequestOptions(
          path: ApiPath.districtsGetAllPath,
        ),
        type: DioExceptionType.connectionError,
        message: LocaleKeys.common_no_internet_connection.tr(),
      );
    }

    try {
      final response = await dio.get(
        ApiPath.districtsGetAllPath,
        queryParameters: {
          'page': page,
          'limit': limit,
          'region': regionId,
        },
      );

      if (response.statusCode == 200) {
        return DistrictsResponse.fromJson(response.data);
      } else {
        throw DioException(
          requestOptions: RequestOptions(path: ApiPath.districtsGetAllPath),
          response: response,
          type: DioExceptionType.badResponse,
          message: 'Server xatoligi',
        );
      }
    } on DioException catch (e) {
      print('DioException in getDistrictsByRegion: $e');
      rethrow;
    } catch (e) {
      print('Exception in getDistrictsByRegion: $e');
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.districtsGetAllPath),
        type: DioExceptionType.unknown,
        error: e,
      );
    }
  }

  /// Get all varieties for tree selection
  Future<VarietiesResponse> getAllVarieties({
    int page = 1,
    int limit = 10,
  }) async {
    // Check network connectivity first
    if (!await networkInfo.isConnected) {
      throw DioException(
        requestOptions: RequestOptions(
          path: ApiPath.varietyPath,
        ),
        type: DioExceptionType.connectionError,
        message: LocaleKeys.common_no_internet_connection.tr(),
      );
    }

    try {
      final response = await dio.get(
        ApiPath.varietyPath,
        queryParameters: {
          'page': page,
          'limit': limit,
        },
      );

      if (response.statusCode == 200) {
        try {
          return VarietiesResponse.fromJson(response.data);
        } catch (parseError) {
          print('Parse error in getAllVarieties: $parseError');
          print('Response data: ${response.data}');
          // Return empty response on parse error
          return VarietiesResponse(
            varieties: [],
            totalDocs: 0,
            limit: limit,
            totalPages: 1,
            page: page,
            hasNextPage: false,
            hasPrevPage: false,
          );
        }
      } else {
        throw DioException(
          requestOptions: RequestOptions(path: ApiPath.varietyPath),
          response: response,
          type: DioExceptionType.badResponse,
          message: 'Server xatoligi',
        );
      }
    } on DioException catch (e) {
      print('DioException in getAllVarieties: $e');
      rethrow;
    } catch (e) {
      print('Exception in getAllVarieties: $e');
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.varietyPath),
        type: DioExceptionType.unknown,
        error: e,
      );
    }
  }
}