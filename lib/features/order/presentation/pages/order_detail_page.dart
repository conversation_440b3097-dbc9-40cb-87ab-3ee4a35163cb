import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:gap/gap.dart';
import 'package:mobil_kochat/core/theme/app_colors.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:share_plus/share_plus.dart';
import '../../../../core/utils/api_path.dart';
import '../../models/order_model.dart';

class OrderDetailPage extends StatelessWidget {
  final Order order;

  const OrderDetailPage({super.key, required this.order});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Buyurtma tafsilotlari',
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: AppColors.cFirstColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: () => _shareOrder(context),
            icon: const Icon(Icons.share),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Order Header
            _buildOrderHeader(),
            const SizedBox(height: 8),

            // Location Information
            _buildLocationInfo(context),
            const SizedBox(height: 8),

            // Photos Section
            if (order.photos.isNotEmpty) ...[
              _buildPhotosSection(),
              const SizedBox(height: 8),
            ],

            // Tree Products Section
            if (order.products.isNotEmpty) ...[
              _buildTreeProductsSection(),
              const SizedBox(height: 8),
            ],

            // Payment Information
            _buildPaymentInfo(),
            const SizedBox(height: 8),

            // Order Status
            // _buildOrderStatus(),
            // const SizedBox(height: 8),

            // Action Buttons
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderHeader() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.receipt_long,
                    color: AppColors.cFirstColor, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'Buyurtma ma\'lumotlari',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildInfoRow('Buyurtmachi:', order.client?.fullName ?? '-'),
            _buildInfoRow('Telefon:', order.client?.phone ?? '-'),
            _buildInfoRow('Sana:', _formatDate(order.createdAt)),
            _buildInfoRow('Vaqt:', order.time)
          ],
        ),
      ),
    );
  }

  Widget _buildLocationInfo(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.location_on, color: AppColors.cFirstColor, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'Joylashuv ma\'lumotlari',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (order.province != null)
              _buildInfoRow('Viloyat:', order.province!.title),
            if (order.region != null)
              _buildInfoRow('Tuman:', order.region!.title),
            if (order.district != null)
              _buildInfoRow('Shahar/Qishloq:', order.district!.title),
            ///Comment our for now
            // _buildInfoRow('Koordinatalar:',
            //     '${order.location.latitude}, ${order.location.longitude}'),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () => _openMap(context),
                icon: const Icon(Icons.map),
                label: const Text('Xaritada ko\'rish'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPhotosSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.photo_camera,
                    color: AppColors.cFirstColor, size: 24),
                const SizedBox(width: 8),
                Text(
                  'Rasmlar (${order.photos.length} ta)',
                  style: const TextStyle(
                      fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            SizedBox(
              height: 120,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: order.photos.length,
                itemBuilder: (context, index) {
                  return Container(
                    margin: const EdgeInsets.only(right: 12),
                    child: GestureDetector(
                      onTap: () => _viewPhoto(context, index),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: CachedNetworkImage(
                          imageUrl: ApiPath.baseUrlFile + order.photos[index],
                          width: 120,
                          height: 120,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => Container(
                            width: 120,
                            height: 120,
                            color: Colors.grey[300],
                            child: const Icon(Icons.image, color: Colors.grey),
                          ),
                          errorWidget: (context, url, error) => Container(
                            width: 120,
                            height: 120,
                            color: Colors.grey[300],
                            child: const Icon(Icons.broken_image,
                                color: Colors.grey),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTreeProductsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.park, color: AppColors.cFirstColor, size: 24),
                const SizedBox(width: 8),
                Text(
                  'Daraxt turlari (${order.products.length} ta)',
                  style: const TextStyle(
                      fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // ✅ Correct loop
            ...order.products.asMap().entries.map((entry) {
              final index = entry.key;
              final product = entry.value;

              return Container(
                margin: const EdgeInsets.only(bottom: 12),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green[200]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${index + 1}. ${product.tree?.name ?? 'Noma\'lum daraxt'}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 4),
                    if (product.tree?.variety != null &&
                        product.tree!.variety.isNotEmpty)
                      _buildInfoRow('Nav:', product.tree!.variety),
                    _buildInfoRow('Soni:', '${product.count} dona'),
                    _buildInfoRow('Narxi:',
                        '${product.price.toStringAsFixed(0)} so\'m/dona'),
                    const SizedBox(height: 4),
                    Text(
                      'Jami: ${product.totalPrice.toStringAsFixed(0)} so\'m',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.green[700],
                        fontSize: 15,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(), // 👈 don’t forget this
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.payment, color: AppColors.cFirstColor, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'To\'lov ma\'lumotlari',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildInfoRow('To\'lov usuli:', order.paymentTypeText),
            _buildInfoRow(
                'Jami summa:', '${order.totalPrice.toStringAsFixed(0)} so\'m'),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderStatus() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info, color: AppColors.cFirstColor, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'Buyurtma holati',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                _buildStatusChip(order.status),
                const Spacer(),
                Text(
                  'Yangilangan: ${_formatDate(order.updatedAt)}',
                  style: const TextStyle(color: Colors.grey, fontSize: 12),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Column(
      children: [
        if (order.pdf != null) ...[
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => _downloadContract(context),
              icon: const Icon(Icons.download),
              label: const Text('Shartnomani yuklab olish'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.cFirstColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(16),
              ),
            ),
          ),
          const SizedBox(height: 12),
        ],
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () => _callClient(context),
            icon: const Icon(Icons.phone),
            label: const Text('Mijozga qo\'ng\'iroq qilish'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.all(16),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Gap(12),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(int status) {
    Color color;
    String text;

    switch (status) {
      case 1:
        color = Colors.orange;
        text = 'Yangi';
        break;
      case 2:
        color = Colors.green;
        text = 'Yakunlangan';
        break;
      case 3:
        color = Colors.red;
        text = 'Bekor qilingan';
        break;
      default:
        color = Colors.grey;
        text = 'Noma\'lum';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 14,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}.${date.month.toString().padLeft(2, '0')}.${date.year}';
  }

  void _shareOrder(BuildContext context) async {
    try {
      final String shareText = _buildShareText();
      await Share.share(
        shareText,
        subject: 'Buyurtma ma\'lumotlari - ${order.id}',
      );
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Ulashishda xatolik yuz berdi')),
        );
      }
    }
  }

  String _buildShareText() {
    final buffer = StringBuffer();
    buffer.writeln('📋 BUYURTMA MA\'LUMOTLARI');
    buffer.writeln('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    buffer.writeln('🆔 Buyurtmachi: ${order.client?.fullName}');
    buffer.writeln('📞 Telefon: ${order.client!.phone}');
    buffer.writeln('📅 Sana: ${_formatDate(order.createdAt)}');
    buffer.writeln('⏰ Vaqt: ${order.time}');
    buffer
        .writeln('💰 Jami summa: ${order.totalPrice.toStringAsFixed(0)} so\'m');
    buffer.writeln();

    if (order.province != null ||
        order.region != null ||
        order.district != null) {
      buffer.writeln('📍 JOYLASHUV');
      buffer.writeln('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
      if (order.province != null)
        buffer.writeln('🏛️ Viloyat: ${order.province!.title}');
      if (order.region != null)
        buffer.writeln('🏘️ Tuman: ${order.region!.title}');
      if (order.district != null)
        buffer.writeln('🏡 Shahar/Qishloq: ${order.district!.title}');
      buffer.writeln(
          '🗺️ Koordinatalar: ${order.location.latitude}, ${order.location.longitude}');
      buffer.writeln();
    }

    if (order.products.isNotEmpty) {
      buffer.writeln('🌳 DARAXT TURLARI');
      buffer.writeln('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
      for (int i = 0; i < order.products.length; i++) {
        final product = order.products[i];
        buffer.writeln('${i + 1}. ${product.tree?.name ?? 'Noma\'lum daraxt'}');
        if (product.tree?.variety != null && product.tree!.variety.isNotEmpty) {
          buffer.writeln('   Nav: ${product.tree!.variety}');
        }
        buffer.writeln('   Soni: ${product.count} dona');
        buffer.writeln(
            '   Narxi: ${product.price.toStringAsFixed(0)} so\'m/dona');
        buffer
            .writeln('   Jami: ${product.totalPrice.toStringAsFixed(0)} so\'m');
        if (i < order.products.length - 1) buffer.writeln();
      }
      buffer.writeln();
    }

    buffer.writeln('💳 TO\'LOV MA\'LUMOTLARI');
    buffer.writeln('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    buffer.writeln('💰 To\'lov usuli: ${order.paymentTypeText}');
    buffer
        .writeln('💵 Jami summa: ${order.totalPrice.toStringAsFixed(0)} so\'m');
    buffer.writeln();

    buffer.writeln('📊 HOLAT: ${order.statusText.toUpperCase()}');
    buffer.writeln('🔄 Yangilangan: ${_formatDate(order.updatedAt)}');

    return buffer.toString();
  }

  void _viewPhoto(BuildContext context, int index) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PhotoViewPage(
          photos: order.photos,
          initialIndex: index,
        ),
      ),
    );
  }

  void _openMap(BuildContext context) async {
    try {
      final double lat = double.tryParse(order.location.latitude) ?? 0.0;
      final double lng = double.tryParse(order.location.longitude) ?? 0.0;

      if (lat == 0.0 && lng == 0.0) {
        // Show error if coordinates are invalid
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Koordinatalar mavjud emas')),
          );
        }
        return;
      }

      // Try to open in Google Maps app first, then fallback to web
      final googleMapsUrl = Uri.parse('comgooglemaps://?q=$lat,$lng');
      final webMapsUrl = Uri.parse('https://maps.google.com/?q=$lat,$lng');

      if (await canLaunchUrl(googleMapsUrl)) {
        await launchUrl(googleMapsUrl);
      } else if (await canLaunchUrl(webMapsUrl)) {
        await launchUrl(webMapsUrl, mode: LaunchMode.externalApplication);
      } else {
        // Fallback: try Apple Maps on iOS
        final appleMapsUrl = Uri.parse('maps://?q=$lat,$lng');
        if (await canLaunchUrl(appleMapsUrl)) {
          await launchUrl(appleMapsUrl);
        } else {
          // No map app available
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Xarita ilovasi topilmadi')),
            );
          }
        }
      }
    } catch (e) {
      // Handle error and show user feedback
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Xaritani ochishda xatolik yuz berdi')),
        );
      }
    }
  }

  void _downloadContract(BuildContext context) async {
    if (order.pdf == null || order.pdf!.isEmpty) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Shartnoma mavjud emas')),
        );
      }
      return;
    }

    try {
      final Uri contractUri = Uri.parse(ApiPath.baseUrlFile + order.pdf!);

      if (await canLaunchUrl(contractUri)) {
        await launchUrl(contractUri, mode: LaunchMode.externalApplication);
      } else {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Shartnomani ochib bo\'lmadi')),
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('Shartnomani yuklab olishda xatolik yuz berdi')),
        );
      }
    }
  }

  void _callClient(BuildContext context) async {
    if (order.client == null || order.client!.phone.isEmpty) {
      return;
    }

    try {
      // Clean the phone number (remove spaces, dashes, etc.)
      String phoneNumber =
          order.client!.phone.replaceAll(RegExp(r'[^\d+]'), '');

      // Ensure the phone number starts with + if it doesn't already
      if (!phoneNumber.startsWith('+')) {
        // Assume Uzbekistan country code if no country code is provided
        if (phoneNumber.length == 9) {
          phoneNumber = '+998$phoneNumber';
        } else if (phoneNumber.length == 12 && phoneNumber.startsWith('998')) {
          phoneNumber = '+$phoneNumber';
        }
      }

      final Uri phoneUri = Uri.parse('tel:$phoneNumber');

      if (await canLaunchUrl(phoneUri)) {
        await launchUrl(phoneUri);
      } else {
        // Handle case where phone app cannot be launched
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Telefon ilovasini ochib bo\'lmadi')),
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('Qo\'ng\'iroq qilishda xatolik yuz berdi')),
        );
      }
    }
  }
}

class PhotoViewPage extends StatelessWidget {
  final List<String> photos;
  final int initialIndex;

  const PhotoViewPage({
    super.key,
    required this.photos,
    required this.initialIndex,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        title: Text('${initialIndex + 1} / ${photos.length}'),
      ),
      body: PageView.builder(
        controller: PageController(initialPage: initialIndex),
        itemCount: photos.length,
        itemBuilder: (context, index) {
          return Center(
            child: InteractiveViewer(
              child: CachedNetworkImage(
                imageUrl: ApiPath.baseUrlFile + photos[index],
                fit: BoxFit.contain,
                placeholder: (context, url) => const Center(
                  child: CircularProgressIndicator(color: Colors.white),
                ),
                errorWidget: (context, url, error) => const Center(
                  child:
                      Icon(Icons.broken_image, color: Colors.white, size: 64),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
