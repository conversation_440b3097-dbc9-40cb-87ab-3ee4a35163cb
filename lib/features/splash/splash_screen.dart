import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mobil_kochat/generated/assets.dart';
import 'package:vibration/vibration.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  // Animation Controllers
  late AnimationController _mainController;
  late AnimationController _logoController;
  late AnimationController _textController;
  late AnimationController _particleController;
  late AnimationController _glowController;

  // Logo Animations
  late Animation<double> _logoFadeAnimation;
  late Animation<double> _logoScaleAnimation;
  late Animation<double> _logoBeatAnimation;

  // Text Animations
  late Animation<double> _textFadeAnimation;
  late Animation<double> _textSlideAnimation;

  // Effect Animations
  late Animation<double> _glowAnimation;
  late Animation<double> _particleAnimation;
  late Animation<double> _progressAnimation;

  // Background gradient animation
  late Animation<double> _backgroundAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimationSequence();
  }

  void _initializeAnimations() {
    // Main controller for overall timing - 3 seconds total
    _mainController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    );

    // Logo animation controller
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    // Text animation controller
    _textController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // Particle animation controller (continuous)
    _particleController = AnimationController(
      duration: const Duration(milliseconds: 4000),
      vsync: this,
    );

    // Glow animation controller (continuous)
    _glowController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    // Logo Animations
    _logoFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: const Interval(0.0, 0.4, curve: Curves.easeOut),
    ));

    _logoScaleAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 0.0, end: 1.0)
            .chain(CurveTween(curve: Curves.elasticOut)),
        weight: 100,
      ),
    ]).animate(CurvedAnimation(
      parent: _logoController,
      curve: const Interval(0.0, 0.7, curve: Curves.easeOut),
    ));

    // Single beat animation - scale up and down once
    _logoBeatAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 1.15)
            .chain(CurveTween(curve: Curves.easeOut)),
        weight: 50,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.15, end: 1.0)
            .chain(CurveTween(curve: Curves.easeIn)),
        weight: 50,
      ),
    ]).animate(CurvedAnimation(
      parent: _logoController,
      curve: const Interval(0.7, 1.0, curve: Curves.easeInOut),
    ));

    // Text Animations
    _textFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));

    _textSlideAnimation = Tween<double>(
      begin: 50.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: const Interval(0.0, 0.8, curve: Curves.easeOut),
    ));

    // Effect Animations
    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _glowController,
      curve: Curves.easeInOut,
    ));

    _particleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _particleController,
      curve: Curves.linear,
    ));

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _mainController,
      curve: const Interval(0.3, 0.9, curve: Curves.easeInOut),
    ));

    _backgroundAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _mainController,
      curve: Curves.easeInOut,
    ));
  }

  Future<void> _startAnimationSequence() async {
    // Start background animation immediately
    _mainController.forward();

    // Start particle animation (continuous loop)
    _particleController.repeat();

    // Start glow animation (continuous loop)
    _glowController.repeat(reverse: true);

    // Delay before logo animation
    await Future.delayed(const Duration(milliseconds: 300));

    // Vibration at logo appearance
    await _triggerVibration(duration: 150);

    // Start logo animation
    _logoController.forward();

    // Delay before text animation
    await Future.delayed(const Duration(milliseconds: 800));

    // Vibration at text appearance
    await _triggerVibration(duration: 100);

    // Start text animation
    _textController.forward();

    // Final vibration at completion
    await Future.delayed(const Duration(milliseconds: 1200));
    await _triggerVibration(duration: 200, pattern: [0, 100, 50, 100]);
  }

  Future<void> _triggerVibration({
    int duration = 100,
    List<int>? pattern,
  }) async {
    try {
      if (await Vibration.hasVibrator() ?? false) {
        if (pattern != null) {
          Vibration.vibrate(pattern: pattern);
        } else {
          Vibration.vibrate(duration: duration);
        }
      }
    } catch (e) {
      // Vibration failed, continue without it
      debugPrint('Vibration failed: $e');
    }
  }

  @override
  void dispose() {
    _mainController.dispose();
    _logoController.dispose();
    _textController.dispose();
    _particleController.dispose();
    _glowController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      body: AnimatedBuilder(
        animation: _backgroundAnimation,
        builder: (context, child) {
          return Container(
            decoration: BoxDecoration(
              gradient: _buildAdaptiveGradient(isDarkMode),
            ),
            child: Stack(
              children: [
                // Animated particles background
                _buildParticleBackground(),

                // Main content
                Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Logo with animations
                      _buildAnimatedLogo(),

                      SizedBox(height: 40.h),

                      // App name with typewriter effect
                      _buildAnimatedText(),

                      SizedBox(height: 60.h),

                      // Loading progress indicator
                      _buildLoadingIndicator(),
                    ],
                  ),
                ),

                // Floating geometric shapes
                _buildFloatingShapes(),
              ],
            ),
          );
        },
      ),
    );
  }

  LinearGradient _buildAdaptiveGradient(bool isDarkMode) {
    if (isDarkMode) {
      return LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          AppColors.cBackgroundColor,
          const Color(0xFF1A1A1A),
          const Color(0xFF0A0A0A),
        ],
        stops: [0.0, 0.6, 1.0],
      );
    } else {
      return LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          AppColors.cLightBackgroundColor,
          const Color(0xFFF0F2F5),
          const Color(0xFFE8EAED),
        ],
        stops: [0.0, 0.6, 1.0],
      );
    }
  }

  Widget _buildAnimatedLogo() {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _logoFadeAnimation,
        _logoScaleAnimation,
        _logoBeatAnimation,
        _glowAnimation,
      ]),
      builder: (context, child) {
        return Opacity(
          opacity: _logoFadeAnimation.value,
          child: Transform.scale(
            scale: _logoScaleAnimation.value * _logoBeatAnimation.value,
            child: Container(
              width: 140.w,
              height: 140.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                boxShadow: [
                  // Animated glow effect
                  BoxShadow(
                    color: AppColors.cFirstColor.withOpacity(
                      _glowAnimation.value * 0.6,
                    ),
                    blurRadius: 30.0 * _glowAnimation.value,
                    spreadRadius: 5.0 * _glowAnimation.value,
                  ),
                  // Secondary glow
                  BoxShadow(
                    color: AppColors.cGreenishColor.withOpacity(
                      _glowAnimation.value * 0.2,
                    ),
                    blurRadius: 50.0 * _glowAnimation.value,
                    spreadRadius: 10.0 * _glowAnimation.value,
                  ),
                ],
              ),
              child: ClipOval(
                child: Container(
                  padding: EdgeInsets.all(20.w),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        AppColors.cFirstColor.withOpacity(0.1),
                        Colors.transparent,
                      ],
                    ),
                  ),
                  child: Image.asset(
                    Assets.assetsLogo,
                    width: 100.w,
                    height: 100.w,
                    fit: BoxFit.contain,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildAnimatedText() {
    const appName = "Mobil Ko'chat";

    return AnimatedBuilder(
      animation: Listenable.merge([
        _textFadeAnimation,
        _textSlideAnimation,
      ]),
      builder: (context, child) {
        final progress = _textFadeAnimation.value;
        final visibleCharCount = (appName.length * progress).round();
        final visibleText = appName.substring(0, visibleCharCount);

        return Transform.translate(
          offset: Offset(0, _textSlideAnimation.value),
          child: Opacity(
            opacity: _textFadeAnimation.value,
            child: Column(
              children: [
                // Main app name with typewriter effect
                ShaderMask(
                  shaderCallback: (bounds) => AppColors.primaryGradient
                      .createShader(bounds),
                  child: Text(
                    visibleText,
                    style: AppTextStyles.headlineLarge.copyWith(
                      fontSize: 32.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      letterSpacing: 2.0,
                    ),
                  ),
                ),

                SizedBox(height: 8.h),

                // Subtitle with fade effect
                Opacity(
                  opacity: _textFadeAnimation.value * 0.8,
                  child: Text(
                    "Yashil hayot garovi!",
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontSize: 14.sp,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? AppColors.cTextGrayColor
                          : AppColors.cLightTextGrayColor,
                      letterSpacing: 1.0,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildLoadingIndicator() {
    return AnimatedBuilder(
      animation: _progressAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _progressAnimation.value,
          child: Column(
            children: [
              // Animated progress ring
              SizedBox(
                width: 60.w,
                height: 60.w,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    // Background ring - same size as progress indicator
                    SizedBox(
                      width: 60.w,
                      height: 60.w,
                      child: CircularProgressIndicator(
                        value: 1.0, // Full circle for background
                        strokeWidth: 3.0,
                        backgroundColor: Colors.transparent,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          (Theme.of(context).brightness == Brightness.dark
                              ? AppColors.cGrayBorderColor
                              : AppColors.cLightBorderColor)
                              .withOpacity(0.3),
                        ),
                      ),
                    ),
                    // Progress ring - perfectly aligned on top
                    SizedBox(
                      width: 60.w,
                      height: 60.w,
                      child: CircularProgressIndicator(
                        value: _progressAnimation.value,
                        strokeWidth: 3.0,
                        backgroundColor: Colors.transparent,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          AppColors.cFirstColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              SizedBox(height: 20.h),

              // Loading dots
              _buildLoadingDots(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLoadingDots() {
    return AnimatedBuilder(
      animation: _particleAnimation,
      builder: (context, child) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(3, (index) {
            final delay = index * 0.2;
            final animationValue = (_particleAnimation.value + delay) % 1.0;
            final scale = 0.5 + (math.sin(animationValue * 2 * math.pi) * 0.5);

            return Container(
              margin: EdgeInsets.symmetric(horizontal: 4.w),
              child: Transform.scale(
                scale: scale,
                child: Container(
                  width: 8.w,
                  height: 8.w,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppColors.cFirstColor.withOpacity(0.8),
                  ),
                ),
              ),
            );
          }),
        );
      },
    );
  }

  Widget _buildParticleBackground() {
    return AnimatedBuilder(
      animation: _particleAnimation,
      builder: (context, child) {
        return Stack(
          children: List.generate(15, (index) {
            final progress = (_particleAnimation.value + (index * 0.1)) % 1.0;
            final size = 2.0 + (index % 3) * 2.0;
            final opacity = 0.1 + (math.sin(progress * 2 * math.pi) * 0.1);

            // Calculate position
            final x = (index * 47.3) % 1.0; // Pseudo-random x
            final y = progress;

            return Positioned(
              left: x * MediaQuery.of(context).size.width,
              top: y * MediaQuery.of(context).size.height,
              child: Container(
                width: size,
                height: size,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppColors.cFirstColor.withOpacity(opacity),
                ),
              ),
            );
          }),
        );
      },
    );
  }

  Widget _buildFloatingShapes() {
    return AnimatedBuilder(
      animation: _particleAnimation,
      builder: (context, child) {
        return Stack(
          children: [
            // Floating triangles
            ...List.generate(5, (index) {
              final progress = (_particleAnimation.value + (index * 0.15)) % 1.0;
              final size = 20.0 + (index * 5.0);
              final rotation = progress * 2 * math.pi;

              return Positioned(
                left: ((index * 73.7) % 1.0) * MediaQuery.of(context).size.width,
                top: ((progress + 0.2) % 1.0) * MediaQuery.of(context).size.height,
                child: Transform.rotate(
                  angle: rotation,
                  child: CustomPaint(
                    size: Size(size, size),
                    painter: TrianglePainter(
                      color: AppColors.cGreenishColor.withOpacity(0.1),
                    ),
                  ),
                ),
              );
            }),

            // Floating squares
            ...List.generate(4, (index) {
              final progress = (_particleAnimation.value + (index * 0.2)) % 1.0;
              final size = 15.0 + (index * 3.0);
              final rotation = progress * math.pi;

              return Positioned(
                right: ((index * 61.3) % 1.0) * MediaQuery.of(context).size.width,
                top: ((progress + 0.5) % 1.0) * MediaQuery.of(context).size.height,
                child: Transform.rotate(
                  angle: rotation,
                  child: Container(
                    width: size,
                    height: size,
                    decoration: BoxDecoration(
                      color: AppColors.cCarrotColor.withOpacity(0.08),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
              );
            }),
          ],
        );
      },
    );
  }
}

// Custom painter for triangle shapes
class TrianglePainter extends CustomPainter {
  final Color color;

  TrianglePainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();
    path.moveTo(size.width / 2, 0);
    path.lineTo(0, size.height);
    path.lineTo(size.width, size.height);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
