import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'tree_model.g.dart';

@JsonSerializable()
class Tree extends Equatable {
  @JsonKey(name: '_id')
  final String id;
  
  final String title;
  final String? desc;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const Tree({
    required this.id,
    required this.title,
    this.desc,
    this.createdAt,
    this.updatedAt,
  });

  factory Tree.fromJson(Map<String, dynamic> json) => _$TreeFromJson(json);
  
  Map<String, dynamic> toJson() => _$TreeToJson(this);

  Tree copyWith({
    String? id,
    String? title,
    String? desc,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Tree(
      id: id ?? this.id,
      title: title ?? this.title,
      desc: desc ?? this.desc,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [id, title, desc, createdAt, updatedAt];

  @override
  String toString() => 'Tree(id: $id, title: $title, desc: $desc)';
}

@JsonSerializable()
class TreeResponse extends Equatable {
  final List<Tree> docs;
  final int totalDocs;
  final int limit;
  final int totalPages;
  final int page;
  final int pagingCounter;
  final bool hasPrevPage;
  final bool hasNextPage;
  final int? prevPage;
  final int? nextPage;

  const TreeResponse({
    required this.docs,
    required this.totalDocs,
    required this.limit,
    required this.totalPages,
    required this.page,
    required this.pagingCounter,
    required this.hasPrevPage,
    required this.hasNextPage,
    this.prevPage,
    this.nextPage,
  });

  factory TreeResponse.fromJson(Map<String, dynamic> json) => _$TreeResponseFromJson(json);
  
  Map<String, dynamic> toJson() => _$TreeResponseToJson(this);

  @override
  List<Object?> get props => [
        docs,
        totalDocs,
        limit,
        totalPages,
        page,
        pagingCounter,
        hasPrevPage,
        hasNextPage,
        prevPage,
        nextPage,
      ];
}
