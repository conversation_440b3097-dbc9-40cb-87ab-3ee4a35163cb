import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'tree_model.dart';

part 'tree_variety_model.g.dart';

@JsonSerializable()
class TreeVariety extends Equatable {
  @Json<PERSON>ey(name: '_id')
  final String id;
  
  final String title;
  final String? desc;
  @JsonKey(defaultValue: 0.0)
  final double price;
  final Tree? tree;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const TreeVariety({
    required this.id,
    required this.title,
    this.desc,
    required this.price,
    this.tree,
    this.createdAt,
    this.updatedAt,
  });

  /// Get formatted price with currency
  String get formattedPrice {
    return '${price.toStringAsFixed(0)} so\'m';
  }

  /// Get price per unit display
  String get priceDisplay {
    return '${price.toStringAsFixed(0)} so\'m/dona';
  }

  factory TreeVariety.fromJson(Map<String, dynamic> json) => _$TreeVarietyFromJson(json);
  
  Map<String, dynamic> toJson() => _$TreeVarietyToJson(this);

  TreeVariety copyWith({
    String? id,
    String? title,
    String? desc,
    double? price,
    Tree? tree,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TreeVariety(
      id: id ?? this.id,
      title: title ?? this.title,
      desc: desc ?? this.desc,
      price: price ?? this.price,
      tree: tree ?? this.tree,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [id, title, desc, price, tree, createdAt, updatedAt];

  @override
  String toString() => 'TreeVariety(id: $id, title: $title, price: $price, tree: ${tree?.title})';
}

@JsonSerializable()
class TreeVarietyResponse extends Equatable {
  final List<TreeVariety> docs;
  final int totalDocs;
  final int limit;
  final int totalPages;
  final int page;
  final int pagingCounter;
  final bool hasPrevPage;
  final bool hasNextPage;
  final int? prevPage;
  final int? nextPage;

  const TreeVarietyResponse({
    required this.docs,
    required this.totalDocs,
    required this.limit,
    required this.totalPages,
    required this.page,
    required this.pagingCounter,
    required this.hasPrevPage,
    required this.hasNextPage,
    this.prevPage,
    this.nextPage,
  });

  factory TreeVarietyResponse.fromJson(Map<String, dynamic> json) => _$TreeVarietyResponseFromJson(json);
  
  Map<String, dynamic> toJson() => _$TreeVarietyResponseToJson(this);

  @override
  List<Object?> get props => [
        docs,
        totalDocs,
        limit,
        totalPages,
        page,
        pagingCounter,
        hasPrevPage,
        hasNextPage,
        prevPage,
        nextPage,
      ];
}

@JsonSerializable()
class TreeVarietyWithQuantity extends Equatable {
  final TreeVariety variety;
  final int quantity;

  const TreeVarietyWithQuantity({
    required this.variety,
    required this.quantity,
  });

  /// Get total price for this variety with quantity
  double get totalPrice => variety.price * quantity;

  /// Get formatted total price
  String get formattedTotalPrice {
    return '${totalPrice.toStringAsFixed(0)} so\'m';
  }

  factory TreeVarietyWithQuantity.fromJson(Map<String, dynamic> json) => _$TreeVarietyWithQuantityFromJson(json);
  
  Map<String, dynamic> toJson() => _$TreeVarietyWithQuantityToJson(this);

  TreeVarietyWithQuantity copyWith({
    TreeVariety? variety,
    int? quantity,
  }) {
    return TreeVarietyWithQuantity(
      variety: variety ?? this.variety,
      quantity: quantity ?? this.quantity,
    );
  }

  @override
  List<Object?> get props => [variety, quantity];

  @override
  String toString() => 'TreeVarietyWithQuantity(variety: ${variety.title}, quantity: $quantity, total: $formattedTotalPrice)';
}
