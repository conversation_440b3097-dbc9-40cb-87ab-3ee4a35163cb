// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tree_variety_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TreeVariety _$TreeVarietyFromJson(Map<String, dynamic> json) => TreeVariety(
      id: json['_id'] as String,
      title: json['title'] as String,
      desc: json['desc'] as String?,
      price: (json['price'] as num).toDouble(),
      tree: json['tree'] == null
          ? null
          : Tree.fromJson(json['tree'] as Map<String, dynamic>),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$TreeVarietyToJson(TreeVariety instance) =>
    <String, dynamic>{
      '_id': instance.id,
      'title': instance.title,
      'desc': instance.desc,
      'price': instance.price,
      'tree': instance.tree,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

TreeVarietyResponse _$TreeVarietyResponseFromJson(Map<String, dynamic> json) =>
    TreeVarietyResponse(
      docs: (json['docs'] as List<dynamic>)
          .map((e) => TreeVariety.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalDocs: (json['totalDocs'] as num).toInt(),
      limit: (json['limit'] as num).toInt(),
      totalPages: (json['totalPages'] as num).toInt(),
      page: (json['page'] as num).toInt(),
      pagingCounter: (json['pagingCounter'] as num).toInt(),
      hasPrevPage: json['hasPrevPage'] as bool,
      hasNextPage: json['hasNextPage'] as bool,
      prevPage: (json['prevPage'] as num?)?.toInt(),
      nextPage: (json['nextPage'] as num?)?.toInt(),
    );

Map<String, dynamic> _$TreeVarietyResponseToJson(
        TreeVarietyResponse instance) =>
    <String, dynamic>{
      'docs': instance.docs,
      'totalDocs': instance.totalDocs,
      'limit': instance.limit,
      'totalPages': instance.totalPages,
      'page': instance.page,
      'pagingCounter': instance.pagingCounter,
      'hasPrevPage': instance.hasPrevPage,
      'hasNextPage': instance.hasNextPage,
      'prevPage': instance.prevPage,
      'nextPage': instance.nextPage,
    };

TreeVarietyWithQuantity _$TreeVarietyWithQuantityFromJson(
        Map<String, dynamic> json) =>
    TreeVarietyWithQuantity(
      variety: TreeVariety.fromJson(json['variety'] as Map<String, dynamic>),
      quantity: (json['quantity'] as num).toInt(),
    );

Map<String, dynamic> _$TreeVarietyWithQuantityToJson(
        TreeVarietyWithQuantity instance) =>
    <String, dynamic>{
      'variety': instance.variety,
      'quantity': instance.quantity,
    };
