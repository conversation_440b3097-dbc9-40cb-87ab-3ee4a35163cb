import 'package:dio/dio.dart';
import 'package:get_storage/get_storage.dart';
import '../../../core/network/network_info.dart';
import '../../../core/utils/api_path.dart';
import '../models/tree_model.dart';
import '../models/tree_variety_model.dart';

class TreeService {
  final Dio _dio;
  final GetStorage _storage;
  final NetworkInfo _networkInfo;

  static const String _treeCacheKey = 'cached_trees';
  static const String _varietyCacheKey = 'cached_varieties';
  static const Duration _cacheValidDuration = Duration(hours: 24);

  TreeService({
    required Dio dio,
    required GetStorage storage,
    required NetworkInfo networkInfo,
  })  : _dio = dio,
        _storage = storage,
        _networkInfo = networkInfo;

  /// Get all trees with caching
  Future<List<Tree>> getTrees({bool forceRefresh = false}) async {
    try {
      // Check cache first
      if (!forceRefresh) {
        final cachedTrees = _getCachedData<Tree>(
          _treeCacheKey,
          (json) => Tree.fromJson(json),
        );
        if (cachedTrees.isNotEmpty) {
          return cachedTrees;
        }
      }

      // Fetch from API
      if (!await _networkInfo.isConnected) {
        // Return cached data even if expired when offline
        return _getCachedData<Tree>(
          _treeCacheKey,
          (json) => Tree.fromJson(json),
          ignoreExpiry: true,
        );
      }

      final response = await _dio.get(ApiPath.treePath);
      
      if (response.statusCode == 200 && response.data != null) {
        final treeResponse = TreeResponse.fromJson(response.data);
        
        // Cache the data
        await _cacheData(_treeCacheKey, treeResponse.docs);
        
        return treeResponse.docs;
      }
    } catch (e) {
      print('Error fetching trees: $e');
      // Return cached data as fallback
      return _getCachedData<Tree>(
        _treeCacheKey,
        (json) => Tree.fromJson(json),
        ignoreExpiry: true,
      );
    }
    
    return [];
  }

  /// Get tree varieties by tree ID with caching
  Future<List<TreeVariety>> getTreeVarieties({
    String? treeId,
    bool forceRefresh = false,
  }) async {
    try {
      final cacheKey = treeId != null 
          ? '${_varietyCacheKey}_$treeId' 
          : _varietyCacheKey;

      // Check cache first
      if (!forceRefresh) {
        final cachedVarieties = _getCachedData<TreeVariety>(
          cacheKey,
          (json) => TreeVariety.fromJson(json),
        );
        if (cachedVarieties.isNotEmpty) {
          return cachedVarieties;
        }
      }

      // Fetch from API
      if (!await _networkInfo.isConnected) {
        return _getCachedData<TreeVariety>(
          cacheKey,
          (json) => TreeVariety.fromJson(json),
          ignoreExpiry: true,
        );
      }

      final queryParams = <String, dynamic>{};
      if (treeId != null) {
        queryParams['tree'] = treeId;
      }

      final response = await _dio.get(
        ApiPath.varietyPath,
        queryParameters: queryParams,
      );
      
      if (response.statusCode == 200 && response.data != null) {
        final varietyResponse = TreeVarietyResponse.fromJson(response.data);
        
        // Cache the data
        await _cacheData(cacheKey, varietyResponse.docs);
        
        return varietyResponse.docs;
      }
    } catch (e) {
      print('Error fetching tree varieties: $e');
      // Return cached data as fallback
      final cacheKey = treeId != null 
          ? '${_varietyCacheKey}_$treeId' 
          : _varietyCacheKey;
      return _getCachedData<TreeVariety>(
        cacheKey,
        (json) => TreeVariety.fromJson(json),
        ignoreExpiry: true,
      );
    }
    
    return [];
  }

  /// Get all varieties (for general use)
  Future<List<TreeVariety>> getAllVarieties({bool forceRefresh = false}) async {
    return getTreeVarieties(forceRefresh: forceRefresh);
  }

  /// Get single tree by ID
  Future<Tree?> getTree(String treeId) async {
    try {
      // First check if we have it in cached trees
      final cachedTrees = _getCachedData<Tree>(
        _treeCacheKey,
        (json) => Tree.fromJson(json),
        ignoreExpiry: true,
      );
      
      final cachedTree = cachedTrees.where((tree) => tree.id == treeId).firstOrNull;
      if (cachedTree != null) {
        return cachedTree;
      }

      // If not cached and we have internet, fetch from API
      if (await _networkInfo.isConnected) {
        final response = await _dio.get('${ApiPath.treePath}/$treeId');
        
        if (response.statusCode == 200 && response.data != null) {
          return Tree.fromJson(response.data);
        }
      }
    } catch (e) {
      print('Error fetching tree: $e');
    }
    
    return null;
  }

  /// Get single variety by ID
  Future<TreeVariety?> getVariety(String varietyId) async {
    try {
      // First check if we have it in cached varieties
      final cachedVarieties = _getCachedData<TreeVariety>(
        _varietyCacheKey,
        (json) => TreeVariety.fromJson(json),
        ignoreExpiry: true,
      );
      
      final cachedVariety = cachedVarieties.where((variety) => variety.id == varietyId).firstOrNull;
      if (cachedVariety != null) {
        return cachedVariety;
      }

      // If not cached and we have internet, fetch from API
      if (await _networkInfo.isConnected) {
        final response = await _dio.get('${ApiPath.varietyPath}/$varietyId');
        
        if (response.statusCode == 200 && response.data != null) {
          return TreeVariety.fromJson(response.data);
        }
      }
    } catch (e) {
      print('Error fetching variety: $e');
    }
    
    return null;
  }

  /// Generic method to get cached data
  List<T> _getCachedData<T>(
    String cacheKey,
    T Function(Map<String, dynamic>) fromJson, {
    bool ignoreExpiry = false,
  }) {
    try {
      final cachedData = _storage.read(cacheKey);
      final cacheTime = _storage.read('${cacheKey}_time');

      if (cachedData == null) return [];

      // Check cache expiry
      if (!ignoreExpiry && cacheTime != null) {
        final cacheDateTime = DateTime.parse(cacheTime);
        final now = DateTime.now();
        if (now.difference(cacheDateTime) > _cacheValidDuration) {
          return []; // Cache expired
        }
      }

      final List<dynamic> dataList = List<dynamic>.from(cachedData);
      return dataList
          .map((item) => fromJson(Map<String, dynamic>.from(item)))
          .toList();
    } catch (e) {
      print('Error reading cached data for $cacheKey: $e');
      return [];
    }
  }

  /// Generic method to cache data
  Future<void> _cacheData<T>(String cacheKey, List<T> data) async {
    try {
      final jsonData = data.map((item) => (item as dynamic).toJson()).toList();
      await _storage.write(cacheKey, jsonData);
      await _storage.write('${cacheKey}_time', DateTime.now().toIso8601String());
    } catch (e) {
      print('Error caching data for $cacheKey: $e');
    }
  }

  /// Clear all tree cache
  Future<void> clearCache() async {
    try {
      final keys = _storage.getKeys();
      for (final key in keys) {
        if (key.toString().startsWith(_treeCacheKey) ||
            key.toString().startsWith(_varietyCacheKey)) {
          await _storage.remove(key);
        }
      }
    } catch (e) {
      print('Error clearing tree cache: $e');
    }
  }

  /// Calculate total price for a list of varieties with quantities
  double calculateTotalPrice(List<TreeVarietyWithQuantity> items) {
    return items.fold(0.0, (total, item) => total + item.totalPrice);
  }

  /// Get formatted total price
  String getFormattedTotalPrice(List<TreeVarietyWithQuantity> items) {
    final total = calculateTotalPrice(items);
    return '${total.toStringAsFixed(0)} so\'m';
  }
}
