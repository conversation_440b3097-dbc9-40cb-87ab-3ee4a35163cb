import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:easy_localization/easy_localization.dart';
import 'core/widgets/custom_bottom_navigation_bar.dart';

import 'features/customer/presentation/page/custumer_page.dart';
import 'features/order/presentation/pages/orders_page.dart';
import 'generated/assets.dart';
import 'translations/locale_keys.g.dart';

class NavigationPage extends StatefulWidget {
  const NavigationPage({
    super.key,
  });

  @override
  State<NavigationPage> createState() => _NavigationPageState();
}

class _NavigationPageState extends State<NavigationPage> {
  int _currentIndex = 0;
  DateTime? _lastBackPressTime;
  bool _isFirstBackPress = true;

  late final List<Widget> _pages;

  @override
  void initState() {
    super.initState();
    _pages = [
      CustomerPage(),
      OrdersPage()
    ];
  }

  List<CustomBottomNavItem> get _bottomNavItems => [
    CustomBottomNavItem(
      iconPath: Assets.iconsProfile,
      label: "<PERSON><PERSON><PERSON><PERSON>",
      activeIconPath: Assets.iconsProfile,
    ),
    CustomBottomNavItem(
      iconPath: Assets.iconsMap,
      activeIconPath: Assets.iconsMap,
      label: "Buyurtmalar",
    ),
    // CustomBottomNavItem(
    //   label: LocaleKeys.navigation_profile.tr(),
    //   iconPath: Assets.iconsProfile,
    //   activeIconPath: Assets.iconsProfile,
    // ),
  ];

  void _handleBackPress() {
    final DateTime now = DateTime.now();

    // If not on the first tab (home), go to first tab
    if (_currentIndex != 0) {
      setState(() {
        _currentIndex = 0;
      });
      _lastBackPressTime = null; // Reset timer when navigating to home
      _isFirstBackPress = true; // Reset first press flag
      return;
    }

    // More aggressive reset: if more than 5 minutes have passed, reset
    if (_lastBackPressTime != null &&
        now.difference(_lastBackPressTime!) > const Duration(minutes: 5)) {
      _lastBackPressTime = null;
      _isFirstBackPress = true;
    }

    // If this is the first back press on home tab OR more than 2 seconds have passed
    if (_isFirstBackPress ||
        _lastBackPressTime == null ||
        now.difference(_lastBackPressTime!) > const Duration(seconds: 2)) {
      _lastBackPressTime = now;
      _isFirstBackPress = false;

      // Show snackbar message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(LocaleKeys.back_again_exit.tr()),
          duration: Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
        ),
      );
    } else {
      // Exit the app
      _lastBackPressTime = null;
      _isFirstBackPress = true;
      SystemNavigator.pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, Object? result) {
        if (didPop) return;
        _handleBackPress();
      },
      child: Scaffold(
        body: _pages[_currentIndex],
        bottomNavigationBar: CustomBottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: (index) {
            setState(() {
              _currentIndex = index;
            });
            // Reset back press timer and flag when user manually switches tabs
            _lastBackPressTime = null;
            _isFirstBackPress = true;
          },
          items: _bottomNavItems,
        ),
      ),
    );
  }
}
