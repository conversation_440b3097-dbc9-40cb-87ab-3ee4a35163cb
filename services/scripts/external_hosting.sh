#!/bin/bash

# External File Hosting Services
# Support for free file hosting services with APIs

# =============================================================================
# EXTERNAL HOSTING CONFIGURATION
# =============================================================================

# Available hosting services
HOSTING_SERVICES=("catbox" "0x0" "fileio" "transfer" "tmpfiles")
DEFAULT_HOSTING_SERVICE="catbox"

# Service configuration functions (instead of associative arrays for compatibility)
get_service_name() {
    case "$1" in
        "catbox") echo "catbox.moe" ;;
        "0x0") echo "0x0.st" ;;
        "fileio") echo "file.io" ;;
        "transfer") echo "transfer.sh" ;;
        "tmpfiles") echo "tmpfiles.org" ;;
        *) echo "unknown" ;;
    esac
}

get_service_max_size() {
    case "$1" in
        "catbox") echo "209715200" ;;   # 200MB
        "0x0") echo "512000000" ;;      # 512MB
        "fileio") echo "104857600" ;;   # 100MB
        "transfer") echo "10737418240" ;; # 10GB
        "tmpfiles") echo "104857600" ;;  # 100MB
        *) echo "52428800" ;;           # Default 50MB
    esac
}

get_service_retention() {
    case "$1" in
        "catbox") echo "permanent" ;;
        "0x0") echo "365 days" ;;
        "fileio") echo "14 days" ;;
        "transfer") echo "14 days" ;;
        "tmpfiles") echo "1 hour to 1 year" ;;
        *) echo "unknown" ;;
    esac
}

# =============================================================================
# EXTERNAL HOSTING FUNCTIONS
# =============================================================================

# Upload file to catbox.moe
upload_to_catbox() {
    local file_path="$1"
    local filename="$(basename "$file_path")"

    log_info "📤 Uploading to catbox.moe..." >&2

    local response
    response=$(curl -s -F "reqtype=fileupload" -F "userhash=5f422a6eddc0da809712db6ff" -F "fileToUpload=@$file_path" https://catbox.moe/user/api.php)

    if [[ $? -eq 0 && -n "$response" && "$response" =~ ^https:// ]]; then
        echo "$response"
        return 0
    else
        log_error "Failed to upload to catbox.moe. Response: $response" >&2
        return 1
    fi
}

# Upload file to 0x0.st
upload_to_0x0() {
    local file_path="$1"
    local filename="$(basename "$file_path")"

    log_info "📤 Uploading to 0x0.st..."

    local response
    response=$(curl -s -H "User-Agent: Mozilla/5.0 (compatible; APK-Upload-Bot/1.0)" -F "file=@$file_path" https://0x0.st)

    if [[ $? -eq 0 && -n "$response" && "$response" =~ ^https:// ]]; then
        echo "$response"
        return 0
    else
        log_error "Failed to upload to 0x0.st. Response: $response"
        return 1
    fi
}

# Upload file to file.io
upload_to_fileio() {
    local file_path="$1"
    local filename="$(basename "$file_path")"
    
    log_info "📤 Uploading to file.io..."
    
    local response
    response=$(curl -s -F "file=@$file_path" https://file.io)
    
    if [[ $? -eq 0 && -n "$response" ]]; then
        # Parse JSON response to extract link
        local link
        link=$(echo "$response" | grep -o '"link":"[^"]*"' | cut -d'"' -f4)
        
        if [[ -n "$link" ]]; then
            echo "$link"
            return 0
        fi
    fi
    
    log_error "Failed to upload to file.io"
    return 1
}

# Upload file to transfer.sh
upload_to_transfer() {
    local file_path="$1"
    local filename="$(basename "$file_path")"
    
    log_info "📤 Uploading to transfer.sh..."
    
    local response
    response=$(curl -s --upload-file "$file_path" "https://transfer.sh/$filename")
    
    if [[ $? -eq 0 && -n "$response" && "$response" =~ ^https:// ]]; then
        echo "$response"
        return 0
    else
        log_error "Failed to upload to transfer.sh"
        return 1
    fi
}

# Upload file to tmpfiles.org
upload_to_tmpfiles() {
    local file_path="$1"
    local filename="$(basename "$file_path")"
    
    log_info "📤 Uploading to tmpfiles.org..."
    
    local response
    response=$(curl -s -F "file=@$file_path" https://tmpfiles.org/api/v1/upload)
    
    if [[ $? -eq 0 && -n "$response" ]]; then
        # Parse JSON response to extract URL
        local url
        url=$(echo "$response" | grep -o '"url":"[^"]*"' | cut -d'"' -f4)
        
        if [[ -n "$url" ]]; then
            # Convert tmpfiles.org URL to direct download link
            local direct_url
            direct_url=$(echo "$url" | sed 's|tmpfiles.org/|tmpfiles.org/dl/|')
            echo "$direct_url"
            return 0
        fi
    fi
    
    log_error "Failed to upload to tmpfiles.org"
    return 1
}

# Upload file to external hosting service
upload_to_external_hosting() {
    local file_path="$1"
    local service="${2:-$DEFAULT_HOSTING_SERVICE}"
    
    if [[ ! -f "$file_path" ]]; then
        log_error "File not found: $file_path"
        return 1
    fi
    
    local file_size
    file_size=$(get_file_size "$file_path")
    local service_name=$(get_service_name "$service")
    local max_size=$(get_service_max_size "$service")
    local retention=$(get_service_retention "$service")

    log_info "🌐 Using external hosting: $service_name" >&2
    log_info "📦 File size: $(format_file_size "$file_size")" >&2
    log_info "📋 Max size: $(format_file_size "$max_size")" >&2
    log_info "⏰ Retention: $retention" >&2
    
    # Check file size limit
    if [[ "$file_size" -gt "$max_size" ]]; then
        log_error "File size ($(format_file_size "$file_size")) exceeds $service_name limit ($(format_file_size "$max_size"))"
        return 1
    fi
    
    # Upload based on service
    case "$service" in
        "catbox")
            upload_to_catbox "$file_path"
            ;;
        "0x0")
            upload_to_0x0 "$file_path"
            ;;
        "fileio")
            upload_to_fileio "$file_path"
            ;;
        "transfer")
            upload_to_transfer "$file_path"
            ;;
        "tmpfiles")
            upload_to_tmpfiles "$file_path"
            ;;
        *)
            log_error "Unknown hosting service: $service"
            return 1
            ;;
    esac
}

# Try multiple hosting services until one succeeds
upload_with_fallback() {
    local file_path="$1"
    local preferred_service="${2:-$DEFAULT_HOSTING_SERVICE}"
    
    # Try preferred service first
    local url
    url=$(upload_to_external_hosting "$file_path" "$preferred_service")
    
    if [[ $? -eq 0 && -n "$url" ]]; then
        echo "$url"
        return 0
    fi
    
    # Try other services as fallback
    log_warning "Preferred service failed, trying fallback services..."
    
    for service in "${HOSTING_SERVICES[@]}"; do
        if [[ "$service" != "$preferred_service" ]]; then
            log_info "🔄 Trying fallback: $(get_service_name "$service")" >&2
            url=$(upload_to_external_hosting "$file_path" "$service")
            
            if [[ $? -eq 0 && -n "$url" ]]; then
                echo "$url"
                return 0
            fi
        fi
    done
    
    log_error "All hosting services failed"
    return 1
}

# List available hosting services
list_hosting_services() {
    echo "Available external hosting services:"
    echo ""
    
    for service in "${HOSTING_SERVICES[@]}"; do
        local name=$(get_service_name "$service")
        local max_size=$(get_service_max_size "$service")
        local retention=$(get_service_retention "$service")

        echo "  $service:"
        echo "    Name: $name"
        echo "    Max Size: $(format_file_size "$max_size")"
        echo "    Retention: $retention"
        echo ""
    done
}

# Get hosting service info
get_hosting_info() {
    local service="$1"
    local name=$(get_service_name "$service")

    if [[ "$name" == "unknown" ]]; then
        echo "Unknown service: $service"
        return 1
    fi

    echo "Service: $name"
    echo "Max Size: $(format_file_size "$(get_service_max_size "$service")")"
    echo "Retention: $(get_service_retention "$service")"
}
